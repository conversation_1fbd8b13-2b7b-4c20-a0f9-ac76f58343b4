import { EAuditStatues, EStatuses, Statues } from "../../../common/constants";
import { EventNames, RoleInfoAccountCol } from "../../constants";
import { EventBus } from "../../eventBus2";
import { clazzLogger } from "../../logger";
import { EVisibility, GUEST_MODE_ALLOW_API_SET } from "./constant";
import { ApiErrors } from "./errorCode";
import { WorkEvent } from "./events/events";
import { CollectModel, CollectRecord } from "./models/collect";
import { LikeModel, LikeRecord } from "./models/like";
import { WorkCols, WorkModel, WorkRecord, WorkVisibility, getBodyShapeFromGenderAndJobId } from "./models/work";
import { FacePinchReq, FacePinchRes } from "./type";
import * as _ from "lodash";
import { searchWork } from "./workSearch/service";
import { ApplyModel, ApplyR<PERSON>ord } from "./models/apply";
import { WorkService } from "./work/service";
import { WeekHotService } from "./weekHot/service";
import { TotalHotService } from "./totalHot/service";
import { nanoid } from "nanoid";
import { facePinchCfg } from "../../common/config";
import { fpClient } from "../../services/fp";
import { FacePinchCurUser } from "./userType";
import { getAccusedRole } from "./user/service";
import { facePinchAuthLogin, userInfoCache } from "./user/auth";
import { UserRecord } from "./models/user";
import { cacheKeyGen } from "../../../common/util2";
import { GenericCache } from "../../common/cacheUtil";
import { ShareVideoModel, ShareVideoRecord } from "./models/shareVideo";
import { checkIsUrl } from "../../services/util";
import { RoleInfoModel } from "../../services/modelProxy";
import { UserCollectService } from "./userCollect/service";
import { validatePartsSelection, getAvailableApplyParts } from './utils/partsUtils';
import { FacePartFilterService } from './services/facePartFilterService';
const logger = clazzLogger("facePinch/operation");

export async function facePinchWorkCloudGameAuthLogin(
  params: FacePinchReq.CloudGameAuthLogin & { ip: string }
): Promise<FacePinchRes.CloudGameAuthLogin> {
  const ret = await facePinchAuthLogin(params);
  return ret;
}

class WorkByShareIdCacheClass extends GenericCache<string, WorkRecord> {
  getExpireTime(shareId: string) {
    return 60;
  }

  getKey(shareId: string): string {
    return cacheKeyGen("face_pinch_work_share_id", { shareId });
  }

  async fetchDataSource(shareId: string): Promise<WorkRecord> {
    const work = await WorkModel.getByShareId(shareId);
    return work;
  }
}

const workByShareIdCache = new WorkByShareIdCacheClass();

/** 捏脸站作品公开的分享信息 */
export async function facePinchWebPublicShareInfo(
  params: FacePinchReq.WebPublicShareInfo
): Promise<FacePinchRes.WebPublicShareInfo> {
  const work = await WorkModel.getByShareId(params.shareId);
  // workByShareIdCache.get(params.shareId)
  if (work) {
    return { shareId: work.shareId, image: work.image };
  } else {
    throw ApiErrors.ShareInfoNotFound;
  }
}

/** 大神作品分享id导入 */
export async function facePinchDashenShareInfoImport(
  params: FacePinchReq.DashenShareInfoImport
): Promise<FacePinchRes.DashenShareInfoImport> {
  const workId = await WorkModel.findOne({ shareId: params.shareId, status: EStatuses.Normal });
  if (!workId) {
    throw ApiErrors.ShareInfoNotFound;
  }
  const id = workId.id;
  const roleInfo = await RoleInfoModel.findOne({ RoleId: params.roleId });
  if (!roleInfo) {
    throw ApiErrors.RoleInfoNotFound;
  }
  const account = roleInfo[RoleInfoAccountCol];
  if (!account) {
    throw ApiErrors.RoleInfoAccountMiss;
  }
  const curUser = await userInfoCache.get({ account, roleId: roleInfo.RoleId });
  return facePinchWorkCollect({ roleid: params.roleId, id, curUser });
}

/** 捏脸视频公开的分享信息 */
export async function facePinchWebPublicShareVideoInfo(
  params: FacePinchReq.WebPublicShareVideoInfo
): Promise<FacePinchRes.WebPublicShareVideoInfo> {
  const r = await ShareVideoModel.findOne({ shareId: params.shareId, status: EStatuses.Normal });
  if (r && r.id) {
    return { shareId: r.shareId, video: r.video };
  } else {
    throw ApiErrors.ShareInfoNotFound;
  }
}

/** 捏脸视频分享id生成 */
export async function facePinchShareVideoAdd(
  params: FacePinchReq.ShareVideoAdd & FacePinchCurUser
): Promise<FacePinchRes.ShareVideoAdd> {
  const curUser = params.curUser;
  const shareId = nanoid();
  if (!checkIsUrl(params.video)) {
    throw ApiErrors.VideoLinkInvalid;
  }
  const props: Omit<ShareVideoRecord, "id"> = {
    userId: curUser.id,
    roleId: curUser.roleId,
    shareId,
    video: params.video,
    status: EStatuses.Normal,
    createTime: Date.now(),
  };
  const id = await ShareVideoModel.insert(props);
  return { shareId: shareId, video: params.video, id };
}

/** 捏脸站作品公开列表 */
export async function facePinchWorkListPublic(
  params: FacePinchReq.WorkListPublic & FacePinchCurUser
): Promise<FacePinchRes.WorkListPublic> {
  if (params.sortBy === "weekHot") {
    return WeekHotService.getPublicList(params);
  } else if (params.sortBy === "hot") {
    return TotalHotService.getPublicList(params);
  } else {
    return WorkService.getPublicListByNew(params);
  }
}

/** 捏脸站作品玩家自己收藏列表 */
export async function facePinchWorkListCollect(
  params: FacePinchReq.WorkListCollect & FacePinchCurUser
): Promise<FacePinchRes.WorkListCollect> {
  const userId = params.curUser.id;

  // 验证部位筛选参数
  const validation = FacePartFilterService.validateAllowedParts(params.allowedParts);
  if (!validation.valid) {
    throw ApiErrors.InvalidPartsSelection;
  }

  const workIds = await CollectModel.getCollectWorkIds(userId);
  let query = WorkModel.collectScope().whereIn("id", workIds);

  // 应用部位筛选
  query = FacePartFilterService.buildFilterQuery(query, params.allowedParts);

  const rows = await WorkModel.powerQuery({
    initQuery: query,
    pagination: { page: params.page, pageSize: params.pageSize },
    orderBy: [["id"], ["desc"]],
  });
  const list = await WorkService.formatListRes(rows, userId);
  const usage = await UserCollectService.getUsage(userId);
  const data: FacePinchRes.WorkListSelf = { list, usage };
  return data;
}

/** 捏脸站作品玩家自己的设计作品 */
export async function facePinchWorkListSelf(
  params: FacePinchReq.WorkListSelf & FacePinchCurUser
): Promise<FacePinchRes.WorkListSelf> {
  const userId = params.curUser.id;

  // 验证部位筛选参数
  const validation = FacePartFilterService.validateAllowedParts(params.allowedParts);
  if (!validation.valid) {
    throw ApiErrors.InvalidPartsSelection;
  }

  let query = WorkModel.selfScope(userId);

  // 应用部位筛选
  query = FacePartFilterService.buildFilterQuery(query, params.allowedParts);

  const rows = await WorkModel.powerQuery({
    initQuery: query,
    pagination: { page: params.page, pageSize: params.pageSize },
    orderBy: [["id"], ["desc"]],
  });
  const list = await WorkService.formatListRes(rows, userId);
  const usage = await WorkModel.getUsage(userId);
  const data: FacePinchRes.WorkListSelf = { list, usage };
  return data;
}

/** 通过分享id获取作品 */
export async function facePinchWorkGetByShareId(
  params: FacePinchReq.WorkGetByShareId
): Promise<FacePinchRes.WorkGetByShareId> {
  const r = await WorkModel.findByShareId(params.shareId);
  checkWorkExist(r);
  const accusedRole = await getAccusedRole(r.userId);

  // 计算可用的应用部位（考虑历史数据限制）
  const availableApplyParts = getAvailableApplyParts(r.allowedParts, r.createTime);

  const data: FacePinchRes.WorkGetByShareId = {
    id: r.id,
    shareId: r.shareId,
    createTime: r.createTime,
    dataUrl: r.dataUrl,
    image: r.image,
    title: r.title,
    desc: r.desc,
    roleName: r.roleName,
    gender: r.gender,
    visibility: r.visibility,
    jobId: r.jobId,
    accusedRole,
    allowedParts: r.allowedParts,
    availableApplyParts: availableApplyParts,
  } as FacePinchRes.WorkGetByShareId;
  return data;
}

function checkWorkExist(r: Pick<WorkRecord, "id" | "userId">) {
  if (r && r.id && r.userId) {
    return r;
  } else {
    throw ApiErrors.WorkNotFound;
  }
}

/** 查看单个作品 */
export async function facePinchWorkDetail(
  params: FacePinchReq.WorkDetail & FacePinchCurUser
): Promise<FacePinchRes.WorkDetail> {
  const userId = params.curUser.id;
  const r = await WorkModel.getByWorkId(params.id);
  checkWorkExist(r);
  const rows = await WorkService.formatWorkRecordList([r], userId);
  const detail = rows[0];
  checkWorkExist(detail);
  const data: FacePinchRes.WorkDetail = detail;
  return data;
}

/** 支持玩家搜索到所有已被公开的作品，不限制职业和性别，但不包含仅个人可见的作品。 */
export async function facePinchWorkSearch(params: FacePinchReq.WorkSearch): Promise<FacePinchRes.WorkSearch> {
  // 验证部位筛选参数
  const validation = FacePartFilterService.validateAllowedParts(params.allowedParts);
  if (!validation.valid) {
    throw ApiErrors.InvalidPartsSelection;
  }

  const rows = await searchWork(params);
  const list = await WorkService.formatWorkRecordList(rows, params.roleid);
  const data: FacePinchRes.WorkSearch = { list };
  return data;
}

export async function facePinchWorkAddForH5GuestUser(params: FacePinchReq.WorkAdd): Promise<FacePinchRes.WorkAdd> {
  const data = await addFacePinchWork(params);
  return data;
}

export async function facePinchWorkAdd(params: FacePinchReq.WorkAdd & FacePinchCurUser): Promise<FacePinchRes.WorkAdd> {
  if (!params.curUser) {
    return facePinchWorkAddForH5GuestUser(params);
  }
  const userId = params.curUser.id;

  // 验证部位选择
  if (!validatePartsSelection(params.allowedParts)) {
    throw ApiErrors.InvalidPartsSelection;
  }

  // 保持原有的上传配额检查（100个上限）
  const hasUploadQuota = await WorkModel.hasUploadQuota(userId);
  if (!hasUploadQuota) {
    throw ApiErrors.WorkUploadReachLimit;
  }

  const data = await addFacePinchWork(params, params.curUser);
  return data;
}

export async function addFacePinchWork(params: FacePinchReq.WorkAdd, curUser?: UserRecord) {
  const userId = curUser ? curUser.id : 0;
  const roleId = curUser ? curUser.roleId : 0;
  const account = curUser ? curUser.account : "";

  const shareId = nanoid();
  const extraProps = {
    roleId,
    userId,
    shareId,
    createTime: Date.now(),
    hot: 0,
    likeCount: 0,
    collectCount: 0,
    applyCount: 0,
  };
  const bodyShape =
    params.bodyShape >= 0 ? params.bodyShape : getBodyShapeFromGenderAndJobId(params.gender, params.jobId);
  const addProps: Omit<WorkRecord, "id"> = {
    title: params.title,
    desc: params.desc,
    account: account,
    userId,
    roleName: params.roleName || "",
    gender: params.gender,
    dataUrl: params.dataUrl,
    image: params.image,
    jobId: params.jobId,
    bodyShape,
    allowedParts: params.allowedParts || 7, // 默认全部允许
    visibility: params.visibility || EVisibility.Public,
    workType: params.workType,
    shareId,
    status: Statues.Normal,
    auditStatus: EAuditStatues.PASS,
    ...extraProps,
  };

  logger.debug({ addProps }, "PrepareAddToWorkModel");
  const id = await WorkModel.insert(addProps);

  if (curUser) {
    const event: WorkEvent.Add = { userId, roleId, work: { id, ...addProps }, ts: Date.now() };
    EventBus.emit(EventNames.FACE_PINCH_WORK$ADD, event);
  }
  const data: FacePinchRes.WorkAdd = {
    id,
    ...extraProps,
  };
  return data;
}

/** 修改作品的可见状态 */
export async function facePinchWorkUpdateVisibility(
  params: FacePinchReq.WorkUpdateVisibility & FacePinchCurUser
): Promise<FacePinchRes.WorkUpdateVisibility> {
  const workId = params.id;
  const userId = params.curUser.id;
  const work = await WorkModel.checkIsOwnWork(workId, userId);
  if (work.visibility === params.visibility) {
    const error =
      work.visibility === WorkVisibility.Private ? ApiErrors.WorkAlreadyPrivate : ApiErrors.WorkAlreadyPublic;
    throw error;
  }
  const ret = await WorkModel.updateByCondition({ id: workId, userId }, { visibility: params.visibility });
  logger.info({ params, ret }, "facePinchWorkUpdateVisibility");
  const event: WorkEvent.WorkUpdateVisibility = {
    userId,
    roleId: params.roleid,
    work,
    ts: Date.now(),
    visibility: params.visibility,
  };
  EventBus.emit(EventNames.FACE_PINCH_WORK$UPDATE_VISIBILITY, event);
  const data: FacePinchRes.WorkUpdateVisibility = {
    isOk: ret.changedRows > 0,
  };
  return data;
}

/** 获取玩家捏脸数据的FilePicker token */
export async function facePinchGetFpToken(
  params: FacePinchReq.GetFpToken & FacePinchCurUser & { ip: string }
): Promise<FacePinchRes.GetFpToken> {
  const uid = getBetterUid(params);
  const token = fpClient.getUploadToken({
    uid,
    mimeLimit: facePinchCfg.mimeLimit,
    fsizeLimit: facePinchCfg.fsizeLimit,
  });
  return token;
}

function getBetterUid(params: FacePinchReq.GetFpToken & FacePinchCurUser & { ip: string }) {
  const curUser = params.curUser;
  let uid = "";
  if (curUser) {
    if (curUser.roleId) {
      uid = curUser.roleId + "";
    } else {
      uid = curUser.account;
    }
  }
  uid = uid || params.ip;
  return uid;
}

/** 删除一个玩家的捏脸作品数据 */
export async function facePinchWorkDel(params: FacePinchReq.WorkDel & FacePinchCurUser): Promise<FacePinchRes.WorkDel> {
  const workId = params.id;
  const userId = params.curUser.id;
  const work = await WorkModel.checkIsOwnWork(workId, userId);
  const ret = await WorkModel.softDeleteByCondition({ id: workId, userId });
  logger.info({ params, ret }, "facePinchWorkDel");
  const event: WorkEvent.Del = { userId, roleId: params.roleid, work, ts: Date.now() };
  EventBus.emit(EventNames.FACE_PINCH_WORK$DEL, event);
  const data: FacePinchRes.WorkDel = {
    isOk: ret.changedRows > 0,
  };
  return data;
}

/** 应用该捏脸作品, 用于捏脸作品被应用于预创建计数(热度计算因子之一) */
export async function facePinchWorkApply(
  params: FacePinchReq.WorkApply & FacePinchCurUser
): Promise<FacePinchRes.WorkApply> {
  const workId = params.id;
  const userId = params.curUser.id;
  const work = await WorkService.checkExist(workId, userId);
  const data: FacePinchRes.WorkApply = {
    isOk: true,
  };
  let isFirstTime = false;
  WorkService.checkPublic(work);
  let id = 0;
  const apply = await ApplyModel.findOne({ userId, workId: workId }, ["id", "roleId", "status"]);
  if (!_.isEmpty(apply) && apply.status === EStatuses.Normal) {
    // 应用过了无需在应用， 直接返回
    return data;
  }
  if (!_.isEmpty(apply)) {
    id = apply.id;
    await ApplyModel.updateById(apply.id, { status: EStatuses.Normal, createTime: Date.now() });
  } else {
    const props: Omit<ApplyRecord, "id"> = {
      userId: params.curUser.id,
      roleId: params.roleid,
      workId,
      targetId: work.userId,
      status: EStatuses.Normal,
      createTime: Date.now(),
    };
    id = await ApplyModel.insert(props);
    isFirstTime = true;
  }
  logger.info({ params, id }, "UserFirstApplyWork");
  const event: WorkEvent.ApplyAdd = { userId, roleId: params.roleid, work, ts: Date.now(), isFirstTime };
  EventBus.emit(EventNames.FACE_PINCH_WORK$APPLY_ADD, event);

  return data;
}

/** 点赞捏脸作品 */
export async function facePinchWorkLike(
  params: FacePinchReq.WorkLike & FacePinchCurUser
): Promise<FacePinchRes.WorkLike> {
  const userId = params.curUser.id;
  const workId = params.id;
  let isFirstTime = false;
  const work = await WorkService.checkExist(workId, userId);
  WorkService.checkPublic(work);
  let id = 0;
  const like = await LikeModel.findOne({ userId, workId: workId }, ["id", "roleId", "status"]);
  if (!_.isEmpty(like) && like.status === EStatuses.Normal) {
    throw ApiErrors.WorkAlreadyLiked;
  }
  if (!_.isEmpty(like)) {
    id = like.id;
    await LikeModel.updateById(like.id, { status: EStatuses.Normal, createTime: Date.now() });
  } else {
    const props: Omit<LikeRecord, "id"> = {
      userId,
      roleId: params.roleid,
      workId,
      targetId: work.userId,
      status: EStatuses.Normal,
      createTime: Date.now(),
    };
    id = await LikeModel.insert(props);
    isFirstTime = true;
  }
  logger.info({ params, id }, "UserLikeWork");
  const event: WorkEvent.LikeAdd = { userId, roleId: params.roleid, work, ts: Date.now(), isFirstTime };
  EventBus.emit(EventNames.FACE_PINCH_WORK$LIKE_ADD, event);

  const data: FacePinchRes.WorkLike = {
    isOk: true,
  };

  return data;
}

/** 点赞捏脸作品 */
export async function facePinchWorkCancelLike(
  params: FacePinchReq.WorkCancelLike & FacePinchCurUser
): Promise<FacePinchRes.WorkCancelLike> {
  const workId = params.id;
  const userId = params.curUser.id;
  const work = await WorkService.checkExist(workId, userId);
  const like = await LikeModel.findOne({ userId, workId }, ["id", "roleId", "status"]);
  if (_.isEmpty(like) || like.status === EStatuses.Deleted) {
    throw ApiErrors.WorkNotLiked;
  } else {
    await LikeModel.softDeleteById(like.id);
    logger.info({ params, id: like.id }, "UserCancelLikeWork");
  }
  const event: WorkEvent.LikeDel = { userId, roleId: params.roleid, work, ts: Date.now() };
  EventBus.emit(EventNames.FACE_PINCH_WORK$LIKE_DEL, event);

  const data: FacePinchRes.WorkCancelLike = {
    isOk: true,
  };

  return data;
}

/** 收藏捏脸作品 */
export async function facePinchWorkCollect(
  params: FacePinchReq.WorkCollect & FacePinchCurUser
): Promise<FacePinchRes.WorkCollect> {
  const userId = params.curUser.id;
  const workId = params.id;
  let isFirstTime = false;

  const hasCollectQuota = await UserCollectService.hasCollectQuota(userId);
  if (!hasCollectQuota) {
    throw ApiErrors.WorkCollectReachLimit;
  }
  const work = await WorkService.checkForImport(params.id);
  WorkService.checkPublic(work);
  let id = 0;
  const collect = await CollectModel.findOne({ userId, workId: workId }, ["id", "roleId", "status"]);
  if (!_.isEmpty(collect) && collect.status === EStatuses.Normal) {
    throw ApiErrors.WorkAlreadyCollected;
  }
  if (!_.isEmpty(collect)) {
    id = collect.id;
    await CollectModel.updateById(collect.id, { status: EStatuses.Normal, createTime: Date.now() });
  } else {
    const props: Omit<CollectRecord, "id"> = {
      userId,
      roleId: params.roleid,
      workId,
      targetId: work.userId,
      status: EStatuses.Normal,
      createTime: Date.now(),
    };
    id = await CollectModel.insert(props);
    isFirstTime = true;
  }
  logger.info({ params, id }, "UserCollectWork");
  const event: WorkEvent.CollectAdd = { userId, roleId: params.roleid, work, ts: Date.now(), isFirstTime };
  EventBus.emit(EventNames.FACE_PINCH_WORK$COLLECT_ADD, event);

  const data: FacePinchRes.WorkCollect = {
    isOk: true,
  };

  return data;
}

/** 取消收藏捏脸作品 */
export async function facePinchWorkCancelCollect(
  params: FacePinchReq.WorkCancelCollect & FacePinchCurUser
): Promise<FacePinchRes.WorkCancelCollect> {
  const workId = params.id;
  const roleId = params.roleid;
  const userId = params.curUser.id;
  const work = await WorkModel.findOne({ id: workId }, WorkCols);
  const collect = await CollectModel.findOne({ userId, workId }, ["id", "roleId", "status"]);
  if (_.isEmpty(collect) || collect.status === EStatuses.Deleted) {
    throw ApiErrors.WorkNotCollected;
  } else {
    await CollectModel.softDeleteById(collect.id);
    logger.info({ params, id: collect.id }, "UserCancelCollectWork");
  }
  const event: WorkEvent.CollectDel = { userId, roleId, work, ts: Date.now() };
  EventBus.emit(EventNames.FACE_PINCH_WORK$COLLECT_DEL, event);

  const data: FacePinchRes.WorkCancelCollect = {
    isOk: true,
  };
  return data;
}

export function isAllowCloudGameGuestAccess(path: string) {
  return GUEST_MODE_ALLOW_API_SET.has(path);
}
