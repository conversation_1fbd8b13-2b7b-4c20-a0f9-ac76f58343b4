"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.isAllowCloudGameGuestAccess = exports.facePinchWorkCancelCollect = exports.facePinchWorkCollect = exports.facePinchWorkCancelLike = exports.facePinchWorkLike = exports.facePinchWorkApply = exports.facePinchWorkDel = exports.facePinchGetFpToken = exports.facePinchWorkUpdateVisibility = exports.addFacePinchWork = exports.facePinchWorkAdd = exports.facePinchWorkAddForH5GuestUser = exports.facePinchWorkSearch = exports.facePinchWorkDetail = exports.facePinchWorkGetByShareId = exports.facePinchWorkListSelf = exports.facePinchWorkListCollect = exports.facePinchWorkListPublic = exports.facePinchShareVideoAdd = exports.facePinchWebPublicShareVideoInfo = exports.facePinchDashenShareInfoImport = exports.facePinchWebPublicShareInfo = exports.facePinchWorkCloudGameAuthLogin = void 0;
const constants_1 = require("../../../common/constants");
const constants_2 = require("../../constants");
const eventBus2_1 = require("../../eventBus2");
const logger_1 = require("../../logger");
const constant_1 = require("./constant");
const errorCode_1 = require("./errorCode");
const collect_1 = require("./models/collect");
const like_1 = require("./models/like");
const work_1 = require("./models/work");
const _ = require("lodash");
const service_1 = require("./workSearch/service");
const apply_1 = require("./models/apply");
const service_2 = require("./work/service");
const service_3 = require("./weekHot/service");
const service_4 = require("./totalHot/service");
const nanoid_1 = require("nanoid");
const config_1 = require("../../common/config");
const fp_1 = require("../../services/fp");
const service_5 = require("./user/service");
const auth_1 = require("./user/auth");
const util2_1 = require("../../../common/util2");
const cacheUtil_1 = require("../../common/cacheUtil");
const shareVideo_1 = require("./models/shareVideo");
const util_1 = require("../../services/util");
const modelProxy_1 = require("../../services/modelProxy");
const service_6 = require("./userCollect/service");
const partsUtils_1 = require("./utils/partsUtils");
const facePartFilterService_1 = require("./services/facePartFilterService");
const logger = (0, logger_1.clazzLogger)("facePinch/operation");
function facePinchWorkCloudGameAuthLogin(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const ret = yield (0, auth_1.facePinchAuthLogin)(params);
        return ret;
    });
}
exports.facePinchWorkCloudGameAuthLogin = facePinchWorkCloudGameAuthLogin;
class WorkByShareIdCacheClass extends cacheUtil_1.GenericCache {
    getExpireTime(shareId) {
        return 60;
    }
    getKey(shareId) {
        return (0, util2_1.cacheKeyGen)("face_pinch_work_share_id", { shareId });
    }
    fetchDataSource(shareId) {
        return __awaiter(this, void 0, void 0, function* () {
            const work = yield work_1.WorkModel.getByShareId(shareId);
            return work;
        });
    }
}
const workByShareIdCache = new WorkByShareIdCacheClass();
/** 捏脸站作品公开的分享信息 */
function facePinchWebPublicShareInfo(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const work = yield work_1.WorkModel.getByShareId(params.shareId);
        // workByShareIdCache.get(params.shareId)
        if (work) {
            return { shareId: work.shareId, image: work.image };
        }
        else {
            throw errorCode_1.ApiErrors.ShareInfoNotFound;
        }
    });
}
exports.facePinchWebPublicShareInfo = facePinchWebPublicShareInfo;
/** 大神作品分享id导入 */
function facePinchDashenShareInfoImport(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const workId = yield work_1.WorkModel.findOne({ shareId: params.shareId, status: 0 /* Normal */ });
        if (!workId) {
            throw errorCode_1.ApiErrors.ShareInfoNotFound;
        }
        const id = workId.id;
        const roleInfo = yield modelProxy_1.RoleInfoModel.findOne({ RoleId: params.roleId });
        if (!roleInfo) {
            throw errorCode_1.ApiErrors.RoleInfoNotFound;
        }
        const account = roleInfo[constants_2.RoleInfoAccountCol];
        if (!account) {
            throw errorCode_1.ApiErrors.RoleInfoAccountMiss;
        }
        const curUser = yield auth_1.userInfoCache.get({ account, roleId: roleInfo.RoleId });
        return facePinchWorkCollect({ roleid: params.roleId, id, curUser });
    });
}
exports.facePinchDashenShareInfoImport = facePinchDashenShareInfoImport;
/** 捏脸视频公开的分享信息 */
function facePinchWebPublicShareVideoInfo(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const r = yield shareVideo_1.ShareVideoModel.findOne({ shareId: params.shareId, status: 0 /* Normal */ });
        if (r && r.id) {
            return { shareId: r.shareId, video: r.video };
        }
        else {
            throw errorCode_1.ApiErrors.ShareInfoNotFound;
        }
    });
}
exports.facePinchWebPublicShareVideoInfo = facePinchWebPublicShareVideoInfo;
/** 捏脸视频分享id生成 */
function facePinchShareVideoAdd(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const curUser = params.curUser;
        const shareId = (0, nanoid_1.nanoid)();
        if (!(0, util_1.checkIsUrl)(params.video)) {
            throw errorCode_1.ApiErrors.VideoLinkInvalid;
        }
        const props = {
            userId: curUser.id,
            roleId: curUser.roleId,
            shareId,
            video: params.video,
            status: 0 /* Normal */,
            createTime: Date.now(),
        };
        const id = yield shareVideo_1.ShareVideoModel.insert(props);
        return { shareId: shareId, video: params.video, id };
    });
}
exports.facePinchShareVideoAdd = facePinchShareVideoAdd;
/** 捏脸站作品公开列表 */
function facePinchWorkListPublic(params) {
    return __awaiter(this, void 0, void 0, function* () {
        if (params.sortBy === "weekHot") {
            return service_3.WeekHotService.getPublicList(params);
        }
        else if (params.sortBy === "hot") {
            return service_4.TotalHotService.getPublicList(params);
        }
        else {
            return service_2.WorkService.getPublicListByNew(params);
        }
    });
}
exports.facePinchWorkListPublic = facePinchWorkListPublic;
/** 捏脸站作品玩家自己收藏列表 */
function facePinchWorkListCollect(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const userId = params.curUser.id;
        // 验证部位筛选参数
        const validation = facePartFilterService_1.FacePartFilterService.validateAllowedParts(params.allowedParts);
        if (!validation.valid) {
            throw errorCode_1.ApiErrors.InvalidPartsSelection;
        }
        const workIds = yield collect_1.CollectModel.getCollectWorkIds(userId);
        let query = work_1.WorkModel.collectScope().whereIn("id", workIds);
        // 应用部位筛选
        query = facePartFilterService_1.FacePartFilterService.buildFilterQuery(query, params.allowedParts);
        const rows = yield work_1.WorkModel.powerQuery({
            initQuery: query,
            pagination: { page: params.page, pageSize: params.pageSize },
            orderBy: [["id"], ["desc"]],
        });
        const list = yield service_2.WorkService.formatListRes(rows, userId);
        const usage = yield service_6.UserCollectService.getUsage(userId);
        const data = { list, usage };
        return data;
    });
}
exports.facePinchWorkListCollect = facePinchWorkListCollect;
/** 捏脸站作品玩家自己的设计作品 */
function facePinchWorkListSelf(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const userId = params.curUser.id;
        // 验证部位筛选参数
        const validation = facePartFilterService_1.FacePartFilterService.validateAllowedParts(params.allowedParts);
        if (!validation.valid) {
            throw errorCode_1.ApiErrors.InvalidPartsSelection;
        }
        let query = work_1.WorkModel.selfScope(userId);
        // 应用部位筛选
        query = facePartFilterService_1.FacePartFilterService.buildFilterQuery(query, params.allowedParts);
        const rows = yield work_1.WorkModel.powerQuery({
            initQuery: query,
            pagination: { page: params.page, pageSize: params.pageSize },
            orderBy: [["id"], ["desc"]],
        });
        const list = yield service_2.WorkService.formatListRes(rows, userId);
        const usage = yield work_1.WorkModel.getUsage(userId);
        const data = { list, usage };
        return data;
    });
}
exports.facePinchWorkListSelf = facePinchWorkListSelf;
/** 通过分享id获取作品 */
function facePinchWorkGetByShareId(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const r = yield work_1.WorkModel.findByShareId(params.shareId);
        checkWorkExist(r);
        const accusedRole = yield (0, service_5.getAccusedRole)(r.userId);
        // 计算可用的应用部位（考虑历史数据限制）
        const availableApplyParts = (0, partsUtils_1.getAvailableApplyParts)(r.allowedParts, r.createTime);
        const data = {
            id: r.id,
            shareId: r.shareId,
            createTime: r.createTime,
            dataUrl: r.dataUrl,
            image: r.image,
            title: r.title,
            desc: r.desc,
            roleName: r.roleName,
            gender: r.gender,
            visibility: r.visibility,
            jobId: r.jobId,
            accusedRole,
            allowedParts: r.allowedParts,
            availableApplyParts: availableApplyParts,
        };
        return data;
    });
}
exports.facePinchWorkGetByShareId = facePinchWorkGetByShareId;
function checkWorkExist(r) {
    if (r && r.id && r.userId) {
        return r;
    }
    else {
        throw errorCode_1.ApiErrors.WorkNotFound;
    }
}
/** 查看单个作品 */
function facePinchWorkDetail(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const userId = params.curUser.id;
        const r = yield work_1.WorkModel.getByWorkId(params.id);
        checkWorkExist(r);
        const rows = yield service_2.WorkService.formatWorkRecordList([r], userId);
        const detail = rows[0];
        checkWorkExist(detail);
        const data = detail;
        return data;
    });
}
exports.facePinchWorkDetail = facePinchWorkDetail;
/** 支持玩家搜索到所有已被公开的作品，不限制职业和性别，但不包含仅个人可见的作品。 */
function facePinchWorkSearch(params) {
    return __awaiter(this, void 0, void 0, function* () {
        // 验证部位筛选参数
        const validation = facePartFilterService_1.FacePartFilterService.validateAllowedParts(params.allowedParts);
        if (!validation.valid) {
            throw errorCode_1.ApiErrors.InvalidPartsSelection;
        }
        const rows = yield (0, service_1.searchWork)(params);
        const list = yield service_2.WorkService.formatWorkRecordList(rows, params.roleid);
        const data = { list };
        return data;
    });
}
exports.facePinchWorkSearch = facePinchWorkSearch;
function facePinchWorkAddForH5GuestUser(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const data = yield addFacePinchWork(params);
        return data;
    });
}
exports.facePinchWorkAddForH5GuestUser = facePinchWorkAddForH5GuestUser;
function facePinchWorkAdd(params) {
    return __awaiter(this, void 0, void 0, function* () {
        if (!params.curUser) {
            return facePinchWorkAddForH5GuestUser(params);
        }
        const userId = params.curUser.id;
        // 验证部位选择
        if (!(0, partsUtils_1.validatePartsSelection)(params.allowedParts)) {
            throw errorCode_1.ApiErrors.InvalidPartsSelection;
        }
        // 保持原有的上传配额检查（100个上限）
        const hasUploadQuota = yield work_1.WorkModel.hasUploadQuota(userId);
        if (!hasUploadQuota) {
            throw errorCode_1.ApiErrors.WorkUploadReachLimit;
        }
        const data = yield addFacePinchWork(params, params.curUser);
        return data;
    });
}
exports.facePinchWorkAdd = facePinchWorkAdd;
function addFacePinchWork(params, curUser) {
    return __awaiter(this, void 0, void 0, function* () {
        const userId = curUser ? curUser.id : 0;
        const roleId = curUser ? curUser.roleId : 0;
        const account = curUser ? curUser.account : "";
        const shareId = (0, nanoid_1.nanoid)();
        const extraProps = {
            roleId,
            userId,
            shareId,
            createTime: Date.now(),
            hot: 0,
            likeCount: 0,
            collectCount: 0,
            applyCount: 0,
        };
        const bodyShape = params.bodyShape >= 0 ? params.bodyShape : (0, work_1.getBodyShapeFromGenderAndJobId)(params.gender, params.jobId);
        const addProps = Object.assign({ title: params.title, desc: params.desc, account: account, userId, roleName: params.roleName || "", gender: params.gender, dataUrl: params.dataUrl, image: params.image, jobId: params.jobId, bodyShape, allowedParts: params.allowedParts || 7, visibility: params.visibility || 0 /* Public */, workType: params.workType, shareId, status: constants_1.Statues.Normal, auditStatus: 1 /* PASS */ }, extraProps);
        logger.debug({ addProps }, "PrepareAddToWorkModel");
        const id = yield work_1.WorkModel.insert(addProps);
        if (curUser) {
            const event = { userId, roleId, work: Object.assign({ id }, addProps), ts: Date.now() };
            eventBus2_1.EventBus.emit(constants_2.EventNames.FACE_PINCH_WORK$ADD, event);
        }
        const data = Object.assign({ id }, extraProps);
        return data;
    });
}
exports.addFacePinchWork = addFacePinchWork;
/** 修改作品的可见状态 */
function facePinchWorkUpdateVisibility(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const workId = params.id;
        const userId = params.curUser.id;
        const work = yield work_1.WorkModel.checkIsOwnWork(workId, userId);
        if (work.visibility === params.visibility) {
            const error = work.visibility === 1 /* Private */ ? errorCode_1.ApiErrors.WorkAlreadyPrivate : errorCode_1.ApiErrors.WorkAlreadyPublic;
            throw error;
        }
        const ret = yield work_1.WorkModel.updateByCondition({ id: workId, userId }, { visibility: params.visibility });
        logger.info({ params, ret }, "facePinchWorkUpdateVisibility");
        const event = {
            userId,
            roleId: params.roleid,
            work,
            ts: Date.now(),
            visibility: params.visibility,
        };
        eventBus2_1.EventBus.emit(constants_2.EventNames.FACE_PINCH_WORK$UPDATE_VISIBILITY, event);
        const data = {
            isOk: ret.changedRows > 0,
        };
        return data;
    });
}
exports.facePinchWorkUpdateVisibility = facePinchWorkUpdateVisibility;
/** 获取玩家捏脸数据的FilePicker token */
function facePinchGetFpToken(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const uid = getBetterUid(params);
        const token = fp_1.fpClient.getUploadToken({
            uid,
            mimeLimit: config_1.facePinchCfg.mimeLimit,
            fsizeLimit: config_1.facePinchCfg.fsizeLimit,
        });
        return token;
    });
}
exports.facePinchGetFpToken = facePinchGetFpToken;
function getBetterUid(params) {
    const curUser = params.curUser;
    let uid = "";
    if (curUser) {
        if (curUser.roleId) {
            uid = curUser.roleId + "";
        }
        else {
            uid = curUser.account;
        }
    }
    uid = uid || params.ip;
    return uid;
}
/** 删除一个玩家的捏脸作品数据 */
function facePinchWorkDel(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const workId = params.id;
        const userId = params.curUser.id;
        const work = yield work_1.WorkModel.checkIsOwnWork(workId, userId);
        const ret = yield work_1.WorkModel.softDeleteByCondition({ id: workId, userId });
        logger.info({ params, ret }, "facePinchWorkDel");
        const event = { userId, roleId: params.roleid, work, ts: Date.now() };
        eventBus2_1.EventBus.emit(constants_2.EventNames.FACE_PINCH_WORK$DEL, event);
        const data = {
            isOk: ret.changedRows > 0,
        };
        return data;
    });
}
exports.facePinchWorkDel = facePinchWorkDel;
/** 应用该捏脸作品, 用于捏脸作品被应用于预创建计数(热度计算因子之一) */
function facePinchWorkApply(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const workId = params.id;
        const userId = params.curUser.id;
        const work = yield service_2.WorkService.checkExist(workId, userId);
        const data = {
            isOk: true,
        };
        let isFirstTime = false;
        service_2.WorkService.checkPublic(work);
        let id = 0;
        const apply = yield apply_1.ApplyModel.findOne({ userId, workId: workId }, ["id", "roleId", "status"]);
        if (!_.isEmpty(apply) && apply.status === 0 /* Normal */) {
            // 应用过了无需在应用， 直接返回
            return data;
        }
        if (!_.isEmpty(apply)) {
            id = apply.id;
            yield apply_1.ApplyModel.updateById(apply.id, { status: 0 /* Normal */, createTime: Date.now() });
        }
        else {
            const props = {
                userId: params.curUser.id,
                roleId: params.roleid,
                workId,
                targetId: work.userId,
                status: 0 /* Normal */,
                createTime: Date.now(),
            };
            id = yield apply_1.ApplyModel.insert(props);
            isFirstTime = true;
        }
        logger.info({ params, id }, "UserFirstApplyWork");
        const event = { userId, roleId: params.roleid, work, ts: Date.now(), isFirstTime };
        eventBus2_1.EventBus.emit(constants_2.EventNames.FACE_PINCH_WORK$APPLY_ADD, event);
        return data;
    });
}
exports.facePinchWorkApply = facePinchWorkApply;
/** 点赞捏脸作品 */
function facePinchWorkLike(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const userId = params.curUser.id;
        const workId = params.id;
        let isFirstTime = false;
        const work = yield service_2.WorkService.checkExist(workId, userId);
        service_2.WorkService.checkPublic(work);
        let id = 0;
        const like = yield like_1.LikeModel.findOne({ userId, workId: workId }, ["id", "roleId", "status"]);
        if (!_.isEmpty(like) && like.status === 0 /* Normal */) {
            throw errorCode_1.ApiErrors.WorkAlreadyLiked;
        }
        if (!_.isEmpty(like)) {
            id = like.id;
            yield like_1.LikeModel.updateById(like.id, { status: 0 /* Normal */, createTime: Date.now() });
        }
        else {
            const props = {
                userId,
                roleId: params.roleid,
                workId,
                targetId: work.userId,
                status: 0 /* Normal */,
                createTime: Date.now(),
            };
            id = yield like_1.LikeModel.insert(props);
            isFirstTime = true;
        }
        logger.info({ params, id }, "UserLikeWork");
        const event = { userId, roleId: params.roleid, work, ts: Date.now(), isFirstTime };
        eventBus2_1.EventBus.emit(constants_2.EventNames.FACE_PINCH_WORK$LIKE_ADD, event);
        const data = {
            isOk: true,
        };
        return data;
    });
}
exports.facePinchWorkLike = facePinchWorkLike;
/** 点赞捏脸作品 */
function facePinchWorkCancelLike(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const workId = params.id;
        const userId = params.curUser.id;
        const work = yield service_2.WorkService.checkExist(workId, userId);
        const like = yield like_1.LikeModel.findOne({ userId, workId }, ["id", "roleId", "status"]);
        if (_.isEmpty(like) || like.status === -1 /* Deleted */) {
            throw errorCode_1.ApiErrors.WorkNotLiked;
        }
        else {
            yield like_1.LikeModel.softDeleteById(like.id);
            logger.info({ params, id: like.id }, "UserCancelLikeWork");
        }
        const event = { userId, roleId: params.roleid, work, ts: Date.now() };
        eventBus2_1.EventBus.emit(constants_2.EventNames.FACE_PINCH_WORK$LIKE_DEL, event);
        const data = {
            isOk: true,
        };
        return data;
    });
}
exports.facePinchWorkCancelLike = facePinchWorkCancelLike;
/** 收藏捏脸作品 */
function facePinchWorkCollect(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const userId = params.curUser.id;
        const workId = params.id;
        let isFirstTime = false;
        const hasCollectQuota = yield service_6.UserCollectService.hasCollectQuota(userId);
        if (!hasCollectQuota) {
            throw errorCode_1.ApiErrors.WorkCollectReachLimit;
        }
        const work = yield service_2.WorkService.checkForImport(params.id);
        service_2.WorkService.checkPublic(work);
        let id = 0;
        const collect = yield collect_1.CollectModel.findOne({ userId, workId: workId }, ["id", "roleId", "status"]);
        if (!_.isEmpty(collect) && collect.status === 0 /* Normal */) {
            throw errorCode_1.ApiErrors.WorkAlreadyCollected;
        }
        if (!_.isEmpty(collect)) {
            id = collect.id;
            yield collect_1.CollectModel.updateById(collect.id, { status: 0 /* Normal */, createTime: Date.now() });
        }
        else {
            const props = {
                userId,
                roleId: params.roleid,
                workId,
                targetId: work.userId,
                status: 0 /* Normal */,
                createTime: Date.now(),
            };
            id = yield collect_1.CollectModel.insert(props);
            isFirstTime = true;
        }
        logger.info({ params, id }, "UserCollectWork");
        const event = { userId, roleId: params.roleid, work, ts: Date.now(), isFirstTime };
        eventBus2_1.EventBus.emit(constants_2.EventNames.FACE_PINCH_WORK$COLLECT_ADD, event);
        const data = {
            isOk: true,
        };
        return data;
    });
}
exports.facePinchWorkCollect = facePinchWorkCollect;
/** 取消收藏捏脸作品 */
function facePinchWorkCancelCollect(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const workId = params.id;
        const roleId = params.roleid;
        const userId = params.curUser.id;
        const work = yield work_1.WorkModel.findOne({ id: workId }, work_1.WorkCols);
        const collect = yield collect_1.CollectModel.findOne({ userId, workId }, ["id", "roleId", "status"]);
        if (_.isEmpty(collect) || collect.status === -1 /* Deleted */) {
            throw errorCode_1.ApiErrors.WorkNotCollected;
        }
        else {
            yield collect_1.CollectModel.softDeleteById(collect.id);
            logger.info({ params, id: collect.id }, "UserCancelCollectWork");
        }
        const event = { userId, roleId, work, ts: Date.now() };
        eventBus2_1.EventBus.emit(constants_2.EventNames.FACE_PINCH_WORK$COLLECT_DEL, event);
        const data = {
            isOk: true,
        };
        return data;
    });
}
exports.facePinchWorkCancelCollect = facePinchWorkCancelCollect;
function isAllowCloudGameGuestAccess(path) {
    return constant_1.GUEST_MODE_ALLOW_API_SET.has(path);
}
exports.isAllowCloudGameGuestAccess = isAllowCloudGameGuestAccess;
//# sourceMappingURL=operation.js.map