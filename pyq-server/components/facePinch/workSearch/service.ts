import { WorkModel } from "../models/work";
import { searchCfg } from "../../../common/config";
import { IWorkSearchApi } from "../userType";
import { FacePinchReq } from "../type";
import { FacePartFilterService } from "../services/facePartFilterService";

class WorkSearchApiByMysql implements IWorkSearchApi {
  static instance: IWorkSearchApi;
  async search(params: FacePinchReq.WorkSearch) {
    const pagination = { page: params.page, pageSize: params.pageSize };
    const kw = params.kw;
    const idRange = await this.getSearchIdRange();
    let query = WorkModel.publicScope().where((query) => {
      query
        .orWhere((query) => {
          query
            .where((query) => {
              query.orWhereBetween("id", idRange).orWhere("roleId", params.roleid);
            })
            .where((query) => {
              query.orWhere("title", "like", `%${WorkModel.escapeLikeStr(kw)}%`);
              query.orWhere("desc", "like", `%${WorkModel.escapeLikeStr(kw)}%`);
            });
        })
        .orWhere((query) => {
          query.where("title", "=", kw);
        });
    });
    query = WorkModel.bodyShapeScope(query, params);

    // 应用部位筛选
    query = FacePartFilterService.buildFilterQuery(query, params.allowedParts);

    const rows = await WorkModel.powerQuery({
      initQuery: query,
      pagination,
      orderBy: [
        ["hot", "id"],
        ["desc", "desc"],
      ],
    });
    return rows;
  }

  async getSearchIdRange(): Promise<[number, number]> {
    const curMaxId = await this.getCurrentMaxMomentId();
    const minId = Math.max(0, curMaxId - searchCfg.maxScanLimit);
    return [minId, curMaxId];
  }

  async getCurrentMaxMomentId() {
    const query = WorkModel.scope().orderBy("id", "desc").limit(1);
    const rows = await WorkModel.smartQuery(query);
    if (rows) {
      return rows[0].id || 0;
    } else {
      return 0;
    }
  }

  static getInstance() {
    if (!this.instance) {
      this.instance = new this();
    }
    return this.instance;
  }
}

export async function searchWork(params: FacePinchReq.WorkSearch) {
  //OPTIMIZE:try other search engine, cause mysql not support full text search well
  return WorkSearchApiByMysql.getInstance().search(params);
}
