openapi: 3.1.1
info:
  title: 倩女手游梦岛接口
  description: 提供倩女手游游戏内朋友圈相关服务
  version: '1.0'
servers:
  - url: https://l10-md-dev.apps-hp.danlu.netease.com/qnm
    description: 国服-测试环境
  - url: http://**************:88/qnm
    description: 国服-测试环境(内网代理)
  - url: https://l10-md-stress.apps.danlu.netease.com/qnm
    description: 国服-压测环境
  - url: http://rc.hi.163.com/qnm
    description: 国服-预发布环境
  - url: https://ccc.hi.163.com/qnm
    description: 国服-正式环境
  - url: http://**************:81/qnm
    description: 港澳台-预发布环境
  - url: https://l10hmtxxak-island.tms.netease.com/qnm
    description: 港澳台-正式环境
  - url: http://*************:81/qnm
    description: 东南亚-预发布环境
  - url: https://l10vn-island.tms.easebar.com/qnm
    description: 东南亚-正式环境
  - url: http://localhost:9992/qnm
    description: 本地环境
security:
  - skeyAuth: []
tags:
  - name: moment_lottery
    description: 动态抽奖
  - name: test
    description: 测试
  - name: fashion_lottery
    description: 时装抽奖
  - name: this_day_that_year
    description: 那年今日
  - name: complain
    description: 举报
  - name: equip
    description: 兵器谱专题
  - name: equip_comment
    description: 兵器谱装备弹幕
  - name: card
    description: 剧情卡相关
  - name: card_notion
    description: 剧情卡的想法相关
  - name: card_notion_comment
    description: 剧情卡的想法的评论相关
  - name: card_notification
    description: 剧情卡通知
  - name: slient_love
    description: 暗恋
  - name: players
    description: 玩家
  - name: server
    description: 游戏服务器接口
  - name: info
    description: 基本状态信息查询
  - name: events
    description: 互动事件(踩空间，送花，送礼)
  - name: auth
    description: 授权相关
  - name: moment
    description: 动态
  - name: at_players
    description: 玩家@接口
  - name: hotMoment
    description: 热门动态
  - name: comment
    description: 评论
  - name: message
    description: 留言
  - name: like
    description: 点赞
  - name: follow
    description: 关注
  - name: inform
    description: 通知
  - name: common
    description: 通用
  - name: topic
    description: 话题
  - name: topic_admin
    description: 话题管理
  - name: rank
    description: 排行
  - name: chat
    description: 聊天相关
  - name: lbs
    description: lbs
  - name: fuxi
    description: 对接伏羲
  - name: misc
    description: 杂项
  - name: ndzj2020
    description: 年度总结2020活动
  - name: activity
    description: 活动
  - name: qmpk
    description: 全民PK
  - name: filepick
    description: filepick指令
  - name: gm
    description: GM指令
  - name: wishlist
    description: 心愿单
  - name: wishlistClient
    description: 心愿单(客户端接口)
  - name: firework_photo
    description: 烟花图片
  - name: firework_photo_server
    description: 烟花图片(服务端接口)
  - name: report_log
    description: 玩家日志服务
  - name: filepick
    description: filepicker服务
  - name: face_pinch
    description: 捏脸相关接口
  - name: home_decorate_photo
    description: 家园自定义装饰图片
  - name: discard
    description: 废弃(需求变动，不再需要, 先关闭)
  - name: server
    description: 服务器调用接口
  - name: kafka
    description: kafka日志消费模拟
  - name: hotMoment
    description: 热门动态
  - name: music_club
    description: 百相演奏-乐团
paths:
  /music_club/create:
    post:
      tags:
        - music_club
      summary: 百相演奏-乐团-创建  (服务端调用)
      operationId: musicClubCreate
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/music-club-create-req'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/music-club-create-resp'
  /music_club/disband:
    post:
      tags:
        - music_club
      summary: 百相演奏-乐团-解散  (服务端调用)
      operationId: musicClubDisband
      security: []
      parameters:
        - name: musicClubId
          in: query
          required: true
          description: 乐团ID
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/music-club-disband-resp'
  /music_club/show:
    get:
      tags:
        - music_club
      summary: 百相演奏-乐团-详情
      description: 获取乐团详情, 返回主打唱片，当前上架唱片数量等信息，注意，这里不校验乐团id，因为服务器请求是，乐团信息可能未同步完成，此时返回符合数据结构的空信息
      operationId: musicClubShow
      security: []
      parameters:
        - name: musicClubId
          in: query
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - data
                properties:
                  code:
                    type: integer
                    format: int32
                    description: 状态码
                    example: 0
                  data:
                    $ref: '#/components/schemas/music-club-show-resp'
  /music_club/update:
    post:
      tags:
        - music_club
      summary: 百相演奏-乐团-更新 (服务端调用)
      operationId: musicClubUpdate
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/music-club-update-req'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/music-club-update-resp'
  /music_club/rank/hot_list:
    get:
      summary: 百相演奏-排行-最热乐团总榜
      description: |
        总热度榜：展示50名。总热度从高到低排序。
      tags:
        - music_club
      operationId: getMusicClubRankHotList
      parameters:
        - $ref: '#/components/parameters/roleid'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - data
                properties:
                  code:
                    type: number
                    example: 0
                  data:
                    type: object
                    required:
                      - list
                      - refreshTime
                    properties:
                      list:
                        type: array
                        items:
                          $ref: '#/components/schemas/music-club-rank-item'
                      refreshTime:
                        type: number
                        description: 排行刷新时间(ms)
                        example: 1716835200000
  /music_club/server/rank/hot_list:
    get:
      summary: 百相演奏-排行-最热乐团总榜 (服务端调用)
      description: |
        总热度榜：展示10名。总热度从高到低排序。
      tags:
        - music_club
      operationId: getMusicClubRankHotListForServer
      security: []
      parameters:
        - type: string
          name: weekDs
          in: query
          description: 周时间戳(yyyyMMdd), 使用每周的周一作为周时间标记
          required: true
          schema:
            type: string
            example: 20250603
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - data
                properties:
                  code:
                    type: number
                    example: 0
                  data:
                    type: object
                    required:
                      - list
                      - refreshTime
                    properties:
                      list:
                        type: array
                        items:
                          $ref: '#/components/schemas/music-club-rank-item'
                      refreshTime:
                        type: number
                        description: 排行刷新时间(ms)
                        example: 1716835200000
  /music_club/rank/week_hot_list:
    get:
      summary: 百相演奏-排行-新晋乐团榜单
      description: |
        新晋榜：展示20名。本周（从周一零点开始算）获取的总热度值最高的队伍。
      tags:
        - music_club
      operationId: getMusicClubRankWeekHotList
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/timestamp'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - data
                properties:
                  code:
                    type: number
                    example: 0
                  data:
                    type: object
                    required:
                      - list
                      - refreshTime
                      - weekDs
                    properties:
                      list:
                        type: array
                        items:
                          $ref: '#/components/schemas/music-club-rank-item'
                      refreshTime:
                        type: number
                        description: 排行刷新时间(ms)
                        example: 1716835200000
                      weekDs:
                        type: string
                        description: 周时间戳(yyyyMMdd)
                        example: 20250603
  /music_club/server/rank/week_hot_list:
    get:
      summary: 百相演奏-排行-新晋乐团榜单 (服务端调用)
      description: |
        新晋榜：展示10名。当前周weekDs（从周一零点开始算）获取的总热度值最高的队伍。
      tags:
        - music_club
      operationId: getMusicClubRankWeekHotListForServer
      security: []
      parameters:
        - type: string
          name: weekDs
          in: query
          description: 周时间戳(yyyyMMdd), 使用每周的周一作为周时间标记
          required: true
          schema:
            type: string
            example: 20250603
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - data
                properties:
                  code:
                    type: number
                    example: 0
                  data:
                    type: object
                    required:
                      - list
                      - refreshTime
                      - weekDs
                    properties:
                      list:
                        type: array
                        items:
                          $ref: '#/components/schemas/music-club-rank-item'
                      refreshTime:
                        type: number
                        description: 排行刷新时间(ms)
                        example: 1716835200000
                      weekDs:
                        type: string
                        description: 周时间戳(yyyyMMdd)
                        example: 20250603
  /music_club/radio/recording_list:
    get:
      summary: 百相演奏-点播-唱片列表
      description: |
        展示唱片列表，用于游戏内点播节目的信息展示
      tags:
        - music_club
      operationId: getMusicClubRecordingList
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/music-club-list-sort'
        - $ref: '#/components/parameters/music-club-id-filter'
        - $ref: '#/components/parameters/commonPage'
        - $ref: '#/components/parameters/commonPageSize'
        - name: kw
          in: query
          description: 搜索关键词, 用于搜索唱片名称
          required: false
          schema:
            type: string
            example: 唱片名字
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - data
                properties:
                  code:
                    type: number
                    example: 0
                  data:
                    type: object
                    required:
                      - list
                    properties:
                      list:
                        type: array
                        items:
                          $ref: '#/components/schemas/music-club-recording-show'
  /music_club/radio/request_play:
    post:
      summary: 百相演奏-点播-同步点播操作行为 (服务端调用)
      description: |
        游戏同步点播操作行为，用于维护增加唱片点播计数
      tags:
        - music_club
      operationId: musicClubRadioRequestPlay
      security: []
      parameters:
        - $ref: '#/components/parameters/roleid'
      requestBody:
        description: 点播请求
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/music-club-radio-request-play-req'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - data
                properties:
                  code:
                    type: number
                    example: 0
                  data:
                    $ref: '#/components/schemas/music-club-radio-request-play-resp'
  /music_club/recording/audit_callback:
    post:
      tags:
        - music_club
      operationId: musicClubRecordingAuditCallback
      summary: 唱片审核回调
      description: 外部审核系统完成审核后调用此接口通知审核结果
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/music-club-recording-audit-callback-req'
      responses:
        '200':
          description: 审核回调处理成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/music-club-recording-audit-callback-resp'
  /music_club/recording/release:
    post:
      summary: 百相演奏-唱片-上架
      description: |
        上架唱片，从本地唱片上架到乐团
      tags:
        - music_club
      operationId: musicClubRecordingRelease
      parameters:
        - $ref: '#/components/parameters/roleid'
      requestBody:
        description: 上架唱片
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/music-club-recording-release-req'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - data
                properties:
                  code:
                    type: number
                    example: 0
                  data:
                    $ref: '#/components/schemas/music-club-recording-release-resp'
  /music_club/recording/remove:
    post:
      summary: 百相演奏-唱片-下架
      description: |
        下架唱片，从乐团中移除该唱片, 只有乐团经理可以下架唱片, 需要*游戏服务端调用*, 完成乐团经理的权限校验
      tags:
        - music_club
      operationId: musicClubRecordingRemove
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/music-club-id'
        - $ref: '#/components/parameters/music-club-recording-id'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - data
                properties:
                  code:
                    type: number
                    example: 0
                  data:
                    $ref: '#/components/schemas/music-club-recording-remove-resp'
  /music_club/recording/rate:
    post:
      summary: 百相演奏-唱片-打分 (服务端接口)
      description: |
        给唱片打分，打分范围0-10分
      tags:
        - music_club
      operationId: musicClubRecordingRate
      security: []
      parameters:
        - $ref: '#/components/parameters/roleid'
      requestBody:
        description: 唱片打分请求
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/music-club-recording-rate-req'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - data
                properties:
                  code:
                    type: number
                    example: 0
                  data:
                    $ref: '#/components/schemas/music-club-recording-rate-resp'
  /music_club/server/recording/show:
    get:
      summary: 百相演奏-唱片-详情 (服务端接口)
      description: |
        获取唱片详细信息，包括乐团信息、点播次数、评分等
      tags:
        - music_club
      operationId: musicClubServerRecordingShow
      security: []
      parameters:
        - name: recordingId
          in: query
          description: 唱片ID
          required: true
          schema:
            type: integer
            minimum: 1
            example: 12345
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - data
                properties:
                  code:
                    type: number
                    example: 0
                  data:
                    $ref: '#/components/schemas/music-club-recording-show'
  /music_club/recording/show:
    get:
      summary: 百相演奏-唱片-详情
      description: |
        获取唱片详细信息，包括乐团信息、点播次数、评分等
      tags:
        - music_club
      operationId: musicClubRecordingShow
      parameters:
        - $ref: '#/components/parameters/roleid'
        - name: recordingId
          in: query
          description: 唱片ID
          required: true
          schema:
            type: integer
            minimum: 1
            example: 12345
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - data
                properties:
                  code:
                    type: number
                    example: 0
                  data:
                    $ref: '#/components/schemas/music-club-recording-show'
  /kafka/music_club/BandHeat:
    post:
      tags:
        - kafka
        - music_club
      security: []
      summary: 乐团-热度变化日志
      description: |
        处理来自游戏的乐团热度变化日志，用于更新乐团总榜单和周榜单。

        日志来源：杨凯 游戏程序 <EMAIL>

        日志格式示例：
        ```
        [2025-06-25 21:55:20][BandHeat],{"server":"1", "band_id":"300001", "band_name":"yk的乐团", "band_level":3, "heat":60, "week_heat":50, "time":1750859718, "show_amount":10, "recording_amount":0, "u_dtls":[]}
        ```
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - server
                - band_id
                - band_name
                - band_level
                - heat
                - week_heat
                - time
              properties:
                server:
                  type: string
                  description: 服务器ID
                  example: '1'
                band_id:
                  type: string
                  description: 乐团ID
                  example: '300001'
                band_name:
                  type: string
                  description: 乐团名称
                  example: yk的乐团
                band_level:
                  type: integer
                  description: 乐团等级
                  example: 3
                heat:
                  type: integer
                  description: 乐团总热度
                  example: 60
                week_heat:
                  type: integer
                  description: 乐团本周获取的热度（每周一0点重置）
                  example: 50
                time:
                  type: integer
                  description: 这次获取的时间戳（秒），用这个时间判断所在的周
                  example: 1750859718
                show_amount:
                  type: integer
                  description: 本次演出加的热度
                  example: 10
                recording_amount:
                  type: integer
                  description: 本次唱片加的热度
                  example: 0
                u_dtls:
                  type: array
                  description: 用户详情列表
                  items:
                    type: object
                  example: []
      responses:
        '200':
          description: 处理成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 响应码，0表示成功
                    example: 0
                  data:
                    type: object
                    description: 处理结果数据
                    properties:
                      updateHotRet:
                        type: object
                        description: 乐团总热度更新结果
                        properties:
                          affectedRows:
                            type: integer
                            description: 影响的行数
                            example: 1
                          changedRows:
                            type: integer
                            description: 改变的行数
                            example: 1
                          insertId:
                            type: integer
                            description: 插入ID
                            example: 0
                      updateWeekHotRet:
                        type: object
                        description: 乐团周热度更新结果
                        properties:
                          affectedRows:
                            type: integer
                            description: 影响的行数
                            example: 1
                          changedRows:
                            type: integer
                            description: 改变的行数
                            example: 1
                          insertId:
                            type: integer
                            description: 插入ID
                            example: 0
        '400':
          description: 请求参数错误
        '500':
          description: 服务器内部错误
      operationId: kafkaMusicClubBandHeat
  /kafka/CreditScore:
    post:
      tags:
        - kafka
      security: []
      summary: 信用分日志
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - server
                - account_id
                - role_id
                - role_name
                - role_level
                - uid
                - mpay_account
                - ip
                - credit_score
                - all_score
              properties:
                server:
                  type: string
                  description: 服务器ID
                  example: '2010'
                account_id:
                  type: string
                  description: 账号ID
                  example: aebfpp4tlufvpc7z@ios.app_store.win.163.com
                role_id:
                  type: string
                  description: 角色ID
                  example: '**********'
                role_name:
                  type: string
                  description: 角色名称
                  example: 卿若惜
                role_level:
                  type: integer
                  description: 角色等级
                  example: 157
                uid:
                  type: string
                  description: 用户ID
                  example: aebfpp4tlufvpc7z
                mpay_account:
                  type: string
                  description: mpay账号
                  example: <EMAIL>
                ip:
                  type: string
                  description: IP地址
                  example: **************
                credit_score:
                  type: integer
                  description: 信用分
                  example: 667
                all_score:
                  type: integer
                  description: 所有分
                  example: 500
      responses:
        '200':
          description: OK
      operationId: kafkaCreditscore
  /kafka/LoginRole_Additional:
    post:
      tags:
        - kafka
      security: []
      summary: 角色登录额外信息日志
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - ip
                - device_model
                - device_height
                - device_width
                - os_name
                - os_ver
                - mac_addr
                - udid
                - nation
                - network
                - app_channel
                - app_ver
                - server
                - account_id
                - role_id
                - role_name
                - role_level
              properties:
                ip:
                  type: string
                  description: IP地址
                  example: ************
                ipv6:
                  type: string
                  description: IPv6地址
                  example: ''
                device_model:
                  type: string
                  description: 设备型号
                  example: Handheld#iPad11,3#6#Metal
                device_height:
                  type: integer
                  description: 设备高度
                  example: 1668
                device_width:
                  type: integer
                  description: 设备宽度
                  example: 2224
                os_name:
                  type: string
                  description: 操作系统名称
                  example: ios
                os_ver:
                  type: string
                  description: 操作系统版本
                  example: iOS 13.7
                mac_addr:
                  type: string
                  description: MAC地址
                  example: 5FCF4F44-033E-4F2F-B07B-ECF9E94586B0
                udid:
                  type: string
                  description: 设备唯一标识
                  example: A11E6BFA-628F-4441-8952-54A87FE8718A
                nation:
                  type: integer
                  description: 国家代码
                  example: 86
                isp:
                  type: string
                  description: 网络服务提供商
                  example: ''
                network:
                  type: string
                  description: 网络类型
                  example: wifi
                app_channel:
                  type: string
                  description: 应用渠道
                  example: app_store
                app_ver:
                  type: string
                  description: 应用版本
                  example: '641100'
                server:
                  type: string
                  description: 服务器ID
                  example: '2043'
                account_id:
                  type: string
                  description: 账号ID
                  example: aebflnbaqab6gh2h@ios.app_store.win.163.com
                old_accountid:
                  type: string
                  description: 旧账号ID
                  example: ''
                role_id:
                  type: string
                  description: 角色ID
                  example: '**********'
                role_name:
                  type: string
                  description: 角色名称
                  example: .。红衣洛洛
                role_level:
                  type: integer
                  description: 角色等级
                  example: 154
                u_vip:
                  type: integer
                  description: VIP等级
                  example: 6
                u_icon:
                  type: string
                  description: 用户图标
                  example: '1'
                u_sch:
                  type: string
                  description: 用户学校
                  example: '1'
                mf:
                  type: integer
                  description: 幸运值
                  example: 301
                xiuwei:
                  type: integer
                  description: 修为值
                  example: 118
                xiulian:
                  type: integer
                  description: 修炼值
                  example: 55
                current_shifu:
                  type: string
                  description: 当前师傅
                  example: '0'
                current_tudi:
                  type: string
                  description: 当前徒弟
                  example: 28607202047,20391602043
                all_shifu:
                  type: array
                  description: 所有师傅
                  items:
                    type: integer
                  example:
                    - 621102044
                    - 652002044
                all_tudi:
                  type: array
                  description: 所有徒弟
                  items:
                    type: integer
                  example:
                    - 578302044
                    - 1637002043
                partner_id:
                  type: string
                  description: 侠侣ID
                  example: '0'
                isCommonDevice:
                  type: integer
                  description: 是否常用设备
                  example: 1
                u_dtls:
                  type: object
                  description: 用户详细信息
                  example:
                    monthcard_left: 16
                    jade_left: 5223
                country_code:
                  type: string
                  description: 国家地区编号
                  example: CN
                max_level:
                  type: integer
                  description: 角色仙凡身等级取较大值
                  example: 154
      responses:
        '200':
          description: OK
      operationId: kafkaLoginroleAdditional
  /kafka/PlayerLevelUp:
    post:
      tags:
        - kafka
      summary: 玩家升级日志
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - server
                - account_id
                - role_id
                - role_name
                - role_level
                - reason
              properties:
                server:
                  type: string
                  description: 服务器ID
                  example: '2483'
                account_id:
                  type: string
                  description: 账号ID
                  example: <EMAIL>
                old_accountid:
                  type: string
                  description: 旧账号ID
                  example: ''
                role_id:
                  type: string
                  description: 角色ID
                  example: '**********'
                role_name:
                  type: string
                  description: 角色名称
                  example: 子夜
                role_level:
                  type: integer
                  description: 角色等级
                  example: 2
                u_vip:
                  type: integer
                  description: VIP等级
                  example: 0
                u_icon:
                  type: string
                  description: 用户图标
                  example: '0'
                u_sch:
                  type: string
                  description: 用户学校
                  example: '9'
                reason:
                  type: string
                  description: 升级原因
                  example: LevelUp
                u_dtls:
                  type: integer
                  description: 用户详细信息
                  example: 38
                ip:
                  type: string
                  description: IP地址
                  example: ***************
                ipv6:
                  type: string
                  description: IPv6地址
                  example: ''
                device_model:
                  type: string
                  description: 设备型号
                  example: Handheld#HONOR PGT-AN10#8#OpenGLES3
                device_height:
                  type: integer
                  description: 设备高度
                  example: 1080
                device_width:
                  type: integer
                  description: 设备宽度
                  example: 2344
                os_name:
                  type: string
                  description: 操作系统名称
                  example: ad
                app_channel:
                  type: string
                  description: 应用渠道
                  example: netease.sub40406_toutiaoys3_cpc_dec
                os_ver:
                  type: string
                  description: 操作系统版本
                  example: Android OS 15 / API-35 (HONORPGT-AN10/9.
                online_time:
                  type: string
                  description: 在线时长
                  example: '197'
                pc_login:
                  type: integer
                  description: 是否PC端登录
                  example: 0
                max_level:
                  type: integer
                  description: 角色仙凡身等级取较大值
                  example: 2
      responses:
        '200':
          description: OK
      operationId: kafkaPlayerlevelup
  /healthCheck:
    get:
      tags:
        - test
      summary: 健康检查
      responses:
        '200':
          description: OK
      operationId: healthCheck
  /moment/tags:
    get:
      tags:
        - moment
      summary: 获取动态允许的标签id列表
      responses:
        '200':
          $ref: '#/components/responses/MomentTagsRes'
      operationId: momentTags
  /at_players/list:
    get:
      tags:
        - at_players
      summary: 玩家动态at列表
      security:
        - skeyAuth: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          $ref: '#/components/responses/AtPlayersListRes'
      operationId: atPlayersList
  /at_players/search:
    get:
      tags:
        - at_players
      summary: 玩家动态at玩家搜索
      security:
        - skeyAuth: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/atPlayersKw'
      responses:
        '200':
          $ref: '#/components/responses/AtPlayersListRes'
      operationId: atPlayersSearch
  /fashion_lottery/list:
    get:
      tags:
        - fashion_lottery
      summary: 时装抽奖获取记录列表
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/fashionLotteryItemLevel'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          $ref: '#/components/responses/FashionLotteryListRes'
      operationId: fashionLotteryList
  /fashion_lottery/long_term_list:
    get:
      tags:
        - fashion_lottery
      summary: 时装抽奖长期类型获取记录列表
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/fashionLotteryItemLevel'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          $ref: '#/components/responses/FashionLotteryListRes'
      operationId: fashionLotteryLongTermList
  /face_pinch/web/public/share_info:
    get:
      tags:
        - face_pinch
      summary: 捏脸-分享-信息
      description: 捏脸站作品公开的分享信息
      parameters:
        - $ref: '#/components/parameters/facePinchShareId'
      responses:
        '200':
          $ref: '#/components/responses/FacePinchWebPublicShareInfoRes'
      operationId: facePinchWebPublicShareInfo
  /face_pinch/web/public/share_video/info:
    get:
      tags:
        - face_pinch
      summary: 捏脸-视频-分享信息
      description: 游戏内捏脸视频分享信息
      parameters:
        - $ref: '#/components/parameters/facePinchShareVideoId'
      responses:
        '200':
          $ref: '#/components/responses/FacePinchWebPublicShareVideoInfoRes'
      operationId: facePinchWebPublicShareVideoInfo
  /face_pinch/work/list_public:
    get:
      tags:
        - face_pinch
      summary: 捏脸-作品-公开列表
      description: 捏脸站作品公开列表
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/skey'
        - $ref: '#/components/parameters/gender'
        - $ref: '#/components/parameters/jobId'
        - $ref: '#/components/schemas/bodyShape'
        - $ref: '#/components/parameters/dateStr'
        - $ref: '#/components/parameters/facePinchSortBy'
        - $ref: '#/components/parameters/facePinchFilterParts'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          $ref: '#/components/responses/FacePinchWorkListRes'
      operationId: facePinchWorkListPublic
  /face_pinch/work/list_collect:
    get:
      tags:
        - face_pinch
      summary: 捏脸-作品-收藏列表
      description: 捏脸站作品玩家自己收藏列表
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/skey'
        - $ref: '#/components/parameters/facePinchFilterParts'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          $ref: '#/components/responses/FacePinchWorkListCollectRes'
      operationId: facePinchWorkListCollect
  /face_pinch/work/list_self:
    get:
      tags:
        - face_pinch
      summary: 捏脸-作品-个人列表
      description: 捏脸站作品玩家自己的设计作品
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/skey'
        - $ref: '#/components/parameters/facePinchFilterParts'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          $ref: '#/components/responses/FacePinchWorkListSelfRes'
      operationId: facePinchWorkListSelf
  /face_pinch/work/get_by_share_id:
    get:
      tags:
        - face_pinch
      summary: 捏脸-作品-分享获取
      description: 通过分享id获取作品
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/skey'
        - $ref: '#/components/parameters/facePinchShareId'
      responses:
        '200':
          $ref: '#/components/responses/FacePinchWorkGetByShareIdRes'
      operationId: facePinchWorkGetByShareId
  /face_pinch/work/detail:
    get:
      tags:
        - face_pinch
      summary: 捏脸-作品-详情
      description: 查看单个作品
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/skey'
        - $ref: '#/components/parameters/facePinchWorkId'
      responses:
        '200':
          $ref: '#/components/responses/FacePinchWorkDetailRes'
      operationId: facePinchWorkDetail
  /face_pinch/work/update_visibility:
    post:
      tags:
        - face_pinch
      summary: 捏脸-作品-更新可见性
      description: 修改作品的可见状态
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/skey'
        - $ref: '#/components/parameters/facePinchWorkId'
        - $ref: '#/components/parameters/facePinchVisibility'
      responses:
        '200':
          $ref: '#/components/responses/FacePinchWorkUpdateVisibilityRes'
      operationId: facePinchWorkUpdateVisibility
  /face_pinch/get_fp_token:
    get:
      tags:
        - face_pinch
      summary: 捏脸-FP令牌-获取
      description: 获取捏脸站fp上传token
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/skey'
      responses:
        '200':
          $ref: '#/components/responses/FacePinchGetFpTokenRes'
      operationId: facePinchGetFpToken
  /face_pinch/work/search:
    get:
      tags:
        - face_pinch
      summary: 捏脸-作品-搜索
      description: 支持玩家搜索到所有已被公开的作品，不限制职业和性别，但不包含仅个人可见的作品。
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/skey'
        - $ref: '#/components/parameters/facePinchKw'
        - $ref: '#/components/schemas/bodyShape'
        - $ref: '#/components/parameters/gender'
        - $ref: '#/components/parameters/jobId'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
        - $ref: '#/components/parameters/facePinchFilterParts'
      responses:
        '200':
          $ref: '#/components/responses/FacePinchWorkListRes'
      operationId: facePinchWorkSearch
  /face_pinch/cloud_game/auth/login:
    post:
      tags:
        - face_pinch
      parameters:
        - $ref: '#/components/parameters/cloudGameRoleId'
        - $ref: '#/components/parameters/cloudGameAccount'
        - $ref: '#/components/parameters/cloudGameTime'
        - $ref: '#/components/parameters/cloudGameAuthToken'
      summary: 捏脸-云游戏-登录
      description: |
        云游戏捏脸相关接口登录, 获取skey接口凭证
        - account使用云游戏登录的账号字段, roleid无可传0,
        - token计算依赖的 FACE_PINCH_CLOUD_AUTH_SALT 联系 <EMAIL> 获取
      responses:
        '200':
          $ref: '#/components/responses/FacePinchCloudGameAuthLoginRes'
      operationId: facePinchCloudGameAuthLogin
  /face_pinch/dashen/share_info/import:
    post:
      tags:
        - face_pinch
      parameters:
        - $ref: '#/components/parameters/facePinchShareId'
        - $ref: '#/components/parameters/roleId'
        - $ref: '#/components/parameters/server'
      summary: 捏脸-数据-导入
      description: 导入捏脸数据到指定roleId
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
      operationId: facePinchDashenShareInfoImport
  /face_pinch/work/add:
    post:
      tags:
        - face_pinch
      summary: 捏脸-作品-新增
      description: 新增一个玩家的捏脸作品数据
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/skey'
      requestBody:
        description: 新增捏脸作品
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FacePinchWorkAdd'
      responses:
        '200':
          $ref: '#/components/responses/FacePinchWorkAddRes'
      operationId: facePinchWorkAdd
  /face_pinch/share_video/add:
    post:
      tags:
        - face_pinch
      summary: 捏脸-视频-新增分享
      description: 给分享视频生成一个分享id
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/skey'
        - $ref: '#/components/parameters/facePinchShareVideo'
      responses:
        '200':
          $ref: '#/components/responses/FacePinchShareVideoAddRes'
      operationId: facePinchShareVideoAdd
  /face_pinch/work/del:
    post:
      tags:
        - face_pinch
      summary: 捏脸-作品-删除
      description: 删除一个玩家的捏脸作品数据
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/facePinchWorkId'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
      operationId: facePinchWorkDel
  /face_pinch/work/apply:
    post:
      tags:
        - face_pinch
      summary: 捏脸-作品-应用
      description: 应用该捏脸作品, 用于捏脸作品被应用于预创建计数(热度计算因子之一)
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/skey'
        - $ref: '#/components/parameters/facePinchWorkId'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
      operationId: facePinchWorkApply
  /face_pinch/work/like:
    post:
      tags:
        - face_pinch
      summary: 捏脸-作品-点赞
      description: 点赞捏脸作品
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/skey'
        - $ref: '#/components/parameters/facePinchWorkId'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
      operationId: facePinchWorkLike
  /face_pinch/work/cancel_like:
    post:
      tags:
        - face_pinch
      summary: 捏脸-作品-取消点赞
      description: 取消点赞捏脸作品
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/skey'
        - $ref: '#/components/parameters/facePinchWorkId'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
      operationId: facePinchWorkCancelLike
  /face_pinch/work/collect:
    post:
      tags:
        - face_pinch
      summary: 捏脸-作品-收藏
      description: 收藏捏脸作品
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/skey'
        - $ref: '#/components/parameters/facePinchWorkId'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
      operationId: facePinchWorkCollect
  /face_pinch/work/cancel_collect:
    post:
      tags:
        - face_pinch
      summary: 捏脸-作品-取消收藏
      description: 取消收藏捏脸作品
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/skey'
        - $ref: '#/components/parameters/facePinchWorkId'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
      operationId: facePinchWorkCancelCollect
  /player/expression_base/update:
    post:
      tags:
        - player
      summary: 更新玩家的头像框
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/skey'
      requestBody:
        description: expression base
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ExpressionBase'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
      operationId: playerExpressionBaseUpdate
  /this_day_that_year/moment_has:
    post:
      tags:
        - this_day_that_year
      summary: 那年今日是否发表过动态
      description: 玩家在往年的同一日期至少发布过一条动态的时候返回为true
      parameters:
        - $ref: '#/components/parameters/roleid'
      responses:
        '200':
          $ref: '#/components/responses/ThisDayThatYearHasMomentRes'
      operationId: thisDayThatYearMomentHas
  /this_day_that_year/moment_list:
    post:
      tags:
        - this_day_that_year
      summary: 那年今日的动态列表
      description: 返回那年同一日的动态列表，按照时间倒序
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pagesize'
      responses:
        '200':
          $ref: '#/components/responses/ThisDayThatYearHasMomentListRes'
      operationId: thisDayThatYearMomentList
  /complain/add:
    post:
      tags:
        - complain
      summary: 添加举报日志
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/skey'
        - $ref: '#/components/parameters/complainTargetUser'
        - $ref: '#/components/parameters/complainResouceType'
        - $ref: '#/components/parameters/complainResouceId'
        - $ref: '#/components/parameters/complainContent'
        - $ref: '#/components/parameters/complainReason'
      responses:
        '200':
          $ref: '#/components/responses/ComplainAddRes'
      operationId: complainAdd
  /equip/comments/create:
    post:
      tags:
        - equip_comment
      summary: 添加兵器谱弹幕
      description: |
        发言限制: *每个玩家每个武器1分钟限制1条弹幕*
      parameters:
        - $ref: '#/components/parameters/equipId'
        - $ref: '#/components/parameters/text'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
      operationId: equipCommentsCreate
  /equip/comments/list:
    post:
      tags:
        - equip_comment
      summary: 兵器谱弹幕装备列表
      parameters:
        - $ref: '#/components/parameters/equipId'
      responses:
        '200':
          $ref: '#/components/responses/EquipCommentListRes'
      operationId: equipCommentsList
  /equip/weapons/filters:
    get:
      tags:
        - equip
      summary: 获取兵器谱排行筛选数据
      responses:
        '200':
          $ref: '#/components/responses/WeaponFiltersRes'
      operationId: equipWeaponsFilters
  /equip/weapons/rank:
    get:
      tags:
        - equip
      summary: 获取兵器谱排行
      parameters:
        - $ref: '#/components/parameters/server_id'
        - $ref: '#/components/parameters/equip_position'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/page_size'
      responses:
        '200':
          $ref: '#/components/responses/EquipWeaponsRankRes'
      operationId: equipWeaponsRank
  /equip/treasures/rank:
    get:
      tags:
        - equip
      summary: 获取奇珍榜排行
      parameters:
        - $ref: '#/components/parameters/server_id'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/page_size'
      responses:
        '200':
          $ref: '#/components/responses/EquipWeaponsRankRes'
      operationId: equipTreasuresRank
  /card/is_enable:
    post:
      tags:
        - card
      summary: 剧情卡功能是否对玩家开放
      parameters:
        - $ref: '#/components/parameters/roleid'
      responses:
        '200':
          $ref: '#/components/responses/CardIsEnableRes'
      operationId: cardIsEnable
  /card/notification/list:
    post:
      tags:
        - card_notification
      summary: 卡片通知列表
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/cardId'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          $ref: '#/components/responses/CardNotificationListRes'
      operationId: cardNotificationList
  /card/notification/del_all:
    post:
      tags:
        - card_notification
      summary: 卡片通知列表全部清空
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/cardId'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
      operationId: cardNotificationDelAll
  /card/notification/new_num:
    post:
      tags:
        - card_notification
      summary: 卡片通知新消息数量
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/cardId'
      responses:
        '200':
          $ref: '#/components/responses/CardNotificationNewNumRes'
      operationId: cardNotificationNewNum
  /card/notification/read_all:
    post:
      tags:
        - card_notification
      summary: 卡片通知列表全部设为已读
      deprecated: true
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/cardId'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
      operationId: cardNotificationReadAll
  /card/notification/read:
    post:
      tags:
        - card_notification
      summary: 卡片通知列表单条已读
      deprecated: true
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/notificationId'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
      operationId: cardNotificationRead
  /card/notion/comment/list:
    post:
      tags:
        - card_notion_comment
      summary: 列出卡片想法评论列表
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/cardNotionId'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          $ref: '#/components/responses/CardNotionCommentListRes'
      operationId: cardNotionCommentList
  /card/notion/comment/add:
    post:
      tags:
        - card_notion_comment
      summary: 添加卡片想法评论
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/cardNotionId'
        - $ref: '#/components/parameters/cardNotionText'
        - $ref: '#/components/parameters/replyCommentId'
      responses:
        '200':
          $ref: '#/components/responses/CommonAddRes'
      operationId: cardNotionCommentAdd
  /card/notion/comment/del:
    post:
      tags:
        - card_notion_comment
      summary: 删除卡片想法评论
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/cardNotionCommentId'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
      operationId: cardNotionCommentDel
  /card/notion/show:
    post:
      tags:
        - card_notion
      summary: 列出单个卡片的想法详情
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/cardNotionId'
      responses:
        '200':
          $ref: '#/components/responses/CardNotionShowRes'
      operationId: cardNotionShow
  /card/notion/list:
    post:
      tags:
        - card_notion
      summary: 列出单个卡片下想法列表
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/cardId'
        - $ref: '#/components/parameters/sortBy'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          $ref: '#/components/responses/CardNotionListRes'
      operationId: cardNotionList
  /card/notion/add:
    post:
      tags:
        - card_notion
      summary: 添加卡片的想法
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/cardId'
        - $ref: '#/components/parameters/text'
      responses:
        '200':
          $ref: '#/components/responses/CommonAddRes'
      operationId: cardNotionAdd
  /card/notion/del:
    post:
      tags:
        - card_notion
      summary: 删除卡片的想法
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/cardNotionId'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
      operationId: cardNotionDel
  /card/notion/like:
    post:
      tags:
        - card_notion
      summary: 给卡片想法点赞
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/cardNotionId'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
      operationId: cardNotionLike
  /card/notion/cancel_like:
    post:
      tags:
        - card_notion
      summary: 给卡片想法取消点赞
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/cardNotionId'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
      operationId: cardNotionCancelLike
  /card/list:
    post:
      tags:
        - card
      summary: 卡片列表统计详情
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/cardIds'
      responses:
        '200':
          $ref: '#/components/responses/CardListInfoRes'
      operationId: cardList
  /card/red_dot:
    post:
      tags:
        - card
      summary: 卡片红点接口(返回所有有新消息的卡片id)
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          $ref: '#/components/responses/CardRedDotRes'
      operationId: cardRedDot
  /card/like:
    post:
      tags:
        - card
      summary: 点赞卡片
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/cardId'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
      operationId: cardLike
  /card/cancel_like:
    post:
      tags:
        - card
      summary: 取消点赞卡片
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/cardId'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
      operationId: cardCancelLike
  /slient_love/add:
    post:
      tags:
        - slient_love
      summary: 添加对方为暗恋
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/targetid'
      responses:
        '200':
          $ref: '#/components/responses/SlientLoveAddRes'
      operationId: slientLoveAdd
  /slient_love/cancel:
    post:
      tags:
        - slient_love
      summary: 取消暗恋
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/targetid'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
      operationId: slientLoveCancel
  /filepick/get_token:
    post:
      tags:
        - filepick
      summary: 获取filepick的token
      parameters:
        - $ref: '#/components/parameters/roleid'
      responses:
        '200':
          description: OK
      operationId: filepickGetToken
  /info/is_enable_island:
    post:
      tags:
        - info
      summary: 查询是否开启梦岛
      parameters:
        - $ref: '#/components/parameters/roleid'
      responses:
        '200':
          description: OK
      operationId: infoIsEnableIsland
  /chat/photo/get:
    post:
      tags:
        - chat
      summary: 获取聊天图片
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/chat_photo_id'
      responses:
        '200':
          description: OK
      operationId: chatPhotoGet
  /chat/photo/create:
    post:
      summary: 保存聊天图片
      tags:
        - chat
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/chat_url'
        - $ref: '#/components/parameters/chat_auth_token'
      responses:
        '200':
          description: OK
      operationId: chatPhotoCreate
  /chat/nos/gettoken:
    post:
      tags:
        - chat
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/chat_nos_type'
      summary: 获取上传聊天图片nosToken
      responses:
        '200':
          description: OK
      operationId: chatNosGettoken
  /chat/nos/get_free_token:
    post:
      tags:
        - chat
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/chat_nos_type'
      summary: 获取上传聊天图片免审核nosToken
      responses:
        '200':
          description: OK
      operationId: chatNosGetFreeToken
  /chat/collect_photo/add:
    post:
      tags:
        - chat
      summary: 添加图片到收藏
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/chat_photo_id'
        - $ref: '#/components/parameters/chat_auth_token'
      responses:
        '200':
          description: OK
      operationId: chatCollectPhotoAdd
  /chat/collect_photo/list:
    post:
      tags:
        - chat
      summary: 图片收藏列表
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/chat_auth_token'
      responses:
        '200':
          description: OK
      operationId: chatCollectPhotoList
  /chat/collect_photo/remove:
    post:
      summary: 从收藏删除图片
      tags:
        - chat
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/chat_photo_ids'
        - $ref: '#/components/parameters/chat_auth_token'
      responses:
        '200':
          description: OK
      operationId: chatCollectPhotoRemove
  /activity/:activity_name/share:
    post:
      tags:
        - activity
      summary: 官网活动分享
      parameters:
        - $ref: '#/components/parameters/activityName'
      requestBody:
        description: share work info
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                works:
                  type: array
                  description: works List
                  items:
                    type: object
                    properties:
                      roleid:
                        type: number
                        example: 1186001000
                      workid:
                        type: number
                        example: 2484
                      cover:
                        type: string
                        example: https://nos.netease.com/hi-163-qnm/qnm/mrt2020_img/********/15899418048153.jpg
                      text:
                        type: string
                        example: <link button=倩影风华,PSHotTalk,2>我正在报名参加2023倩影风华，快来给我投票送花吧！
      responses:
        '200':
          description: OK
      operationId: activity:activityNameShare
  /activity/ndzj2020/moment/most_liked:
    get:
      tags:
        - ndzj2020
      summary: 年度总结2020点赞最多动态
      parameters:
        - $ref: '#/components/parameters/roleid'
      responses:
        '200':
          description: OK
          content:
            application/json:
              examples:
                HasMoment:
                  summary: 大于100赞的返回
                  value:
                    code: 0
                    data:
                      hasHotMoment: true
                      moment:
                        id: 15938801960
                        likeCount: 3403
                        text: <link item=21102081,AwEBAAvSTlsAAAAA,二变易容丹,FFFFFF>
                        imgList: []
                        videoList: []
                        createTime: 1577908000000
                NoMoment:
                  summary: 没有大于100赞动态时的返回
                  value:
                    code: 0
                    data:
                      hasHotMoment: false
                      moment: null
              schema:
                description: ''
                type: object
                properties:
                  code:
                    type: number
                  data:
                    type: object
                    properties:
                      hasHotMoment:
                        type: boolean
                      moment:
                        type: object
                        properties:
                          id:
                            type: number
                          likeCount:
                            type: number
                          text:
                            type: string
                            minLength: 1
                          imgList:
                            type: array
                            items:
                              type: string
                          videoList:
                            type: array
                            items:
                              type: string
                          createTime:
                            type: number
                        required:
                          - id
                          - likeCount
                          - text
                          - imgList
                          - videoList
                          - createTime
                    required:
                      - hasHotMoment
                      - moment
                required:
                  - code
                  - data
      operationId: activityNdzj2020MomentMostLiked
  /activity/ndzj2020/wish/help_rank:
    get:
      tags:
        - ndzj2020
      summary: 年度总结2020助力心愿助力排行
      responses:
        '200':
          description: OK
          content:
            application/json:
              examples:
                RankData:
                  summary: 心愿助力排行数据
                  value:
                    code: 0
                    data:
                      list:
                        - rank: 1
                          roleId: 4263401014
                          serverId: 1014
                          roleName: 人称小吕布つ
                          progress: 650
              schema:
                description: ''
                type: object
                properties:
                  code:
                    type: number
                  data:
                    type: object
                    properties:
                      list:
                        type: array
                        uniqueItems: true
                        minItems: 1
                        items:
                          required:
                            - rank
                            - roleId
                            - serverId
                            - roleName
                            - progress
                          properties:
                            rank:
                              type: number
                            roleId:
                              type: number
                            serverId:
                              type: number
                            roleName:
                              type: string
                              minLength: 1
                            progress:
                              type: number
                    required:
                      - list
                required:
                  - code
                  - data
      operationId: activityNdzj2020WishHelpRank
  /activity/ndzj2020/wish/recent_helps:
    get:
      tags:
        - ndzj2020
      summary: 年度总结2020心最近助力心愿列表
      responses:
        '200':
          description: OK
          content:
            application/json:
              examples:
                RecentHelpList:
                  value:
                    code: 0
                    data:
                      list:
                        - id: 223
                          roleId: 4263401014
                          roleName: 人称小吕布つ
                          targetId: 4263401014
                          targetName: 杀神·滔
                          createTime: 1607312514029
                      count: 1
              schema:
                description: ''
                type: object
                properties:
                  code:
                    type: number
                  data:
                    type: object
                    properties:
                      list:
                        type: array
                        uniqueItems: true
                        minItems: 1
                        items:
                          required:
                            - id
                            - roleId
                            - roleName
                            - targetId
                            - targetName
                            - createTime
                          properties:
                            id:
                              type: number
                            roleId:
                              type: number
                            roleName:
                              type: string
                              minLength: 1
                            targetId:
                              type: number
                            targetName:
                              type: string
                              minLength: 1
                            createTime:
                              type: number
                      count:
                        type: number
                    required:
                      - list
                      - count
                required:
                  - code
                  - data
      operationId: activityNdzj2020WishRecentHelps
  /activity/login:
    get:
      tags:
        - activity
      summary: 活动自动登录
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/skey'
        - $ref: '#/components/parameters/activityLoginMode'
        - name: activity_name
          description: 活动名
          in: query
          schema:
            type: string
            example: DoubleEleven2019
        - name: redirect_url
          description: 自动登录的活动页面, 不使用活动名跳转时，使用该参数控制跳转页面
          in: query
          schema:
            type: string
            example: ''
      responses:
        '200':
          description: OK
      operationId: activityLogin
  /activity/login/bridge:
    get:
      tags:
        - activity
      summary: 活动自动登录中转页(jsBridge)
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/skey'
        - name: activity_name
          description: 活动名
          in: query
          schema:
            type: string
            example: DoubleEleven2019
        - name: redirect_url
          description: 自动登录的活动页面, 不使用活动名跳转时，使用该参数控制跳转页面
          in: query
          schema:
            type: string
      responses:
        '200':
          description: OK
      operationId: activityLoginBridge
  /activity/login/validate_access_token:
    get:
      tags:
        - activity
      summary: 验证自动登录产生的token并获取相关信息
      parameters:
        - name: access_token
          in: query
          description: 自动登录生成的验证token
          schema:
            type: string
            example: accessTokenIssuedByActivityLoginEndPoint
      responses:
        '200':
          $ref: '#/components/responses/ActivityLoginValidateAccessTokenRes'
      operationId: activityLoginValidateAccessToken
  /activity/add_moment:
    post:
      summary: 活动分享到梦岛
      description: |
        ## 10参与话题是个特殊的文本链接
        `<link button=笛子特效,PSHotTalk,12>话题+文案+表情#24#28` <br>
        `<link button=笛子特效,PSHotTalk,12>`<br>
        `<link button={话题名字},PSHotTalk,{话题id}>` <br>
        按照这个规则来生成， 话题id要等话题管理后台上线了话题才有的。 这两个变量建议放在配置中
      tags:
        - activity
      requestBody:
        description: add moment body
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                roleId:
                  type: number
                  description: 玩家id
                  example: *********
                text:
                  type: string
                  description: 心情文本
                  example: test to add
                imgs:
                  type: array
                  description: 分享图片列表
                  items:
                    type: string
                    example: http://hi-163-qnm.nosdn.127.net/moment/201609/01/67f70920700111e6a36399e1934d111d
                skipImgAudit:
                  description: 是否跳过图片审核
                  type: number
                  enum:
                    - 0
                    - 1
                  example: 1
                emitTopicEvent:
                  description: 是否触发动态添加到话题事件
                  type: number
                  enum:
                    - 0
                    - 1
                videos:
                  type: array
                  description: 分享视频列表
                  items:
                    type: string
                    example: http://hi-163-qnm.nosdn.127.net/upload/201905/13/deaab8a0754e11e9bbd4a9db24757383.mp4
                skipVideoAudit:
                  description: 是否跳过视频审核
                  type: number
                  enum:
                    - 0
                    - 1
                showTopicPage:
                  description: 是否出现在话题页 (当文本中附带话题链接才生效, 默认出现)
                  type: number
                  enum:
                    - 0
                    - 1
                  example: 1
      responses:
        '200':
          description: OK
      operationId: activityAddMoment
  /activity/qmpk/rank:
    post:
      summary: 全面PK排行
      tags:
        - qmpk
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/qmpkDate'
      responses:
        '200':
          description: OK
      operationId: activityQmpkRank
  /activity/qmpk/rank2:
    get:
      summary: 全面PK排行(PHP后台访问, ip校验)
      tags:
        - qmpk
      parameters:
        - $ref: '#/components/parameters/qmpkDate'
      responses:
        '200':
          description: OK
      operationId: activityQmpkRank2
  /gm_cmd/inform/unread_all:
    post:
      tags:
        - gm
        - inform
      summary: GM指令-通知-标记所有未读
      description: 通过玩家ID将该玩家的所有通知状态设置为未读
      operationId: markAllInformUnread
      parameters:
        - $ref: '#/components/parameters/roleid'
      responses:
        '200':
          description: 操作成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 状态码
                    example: 0
                  data:
                    type: object
                    properties:
                      markedCount:
                        type: integer
                        description: 标记为未读的消息数量
                        example: 10
  /gm_cmd/moment/search:
    post:
      summary: GM指令-查找玩家的某条动态
      tags:
        - gm
        - moment_lottery
      security: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/kw'
      responses:
        '200':
          description: OK
      operationId: gmCmdMomentSearch
  /gm_cmd/moment_lottery/draw:
    post:
      summary: GM指令-抽奖动态触发开奖
      tags:
        - gm
        - moment_lottery
      security: []
      parameters:
        - $ref: '#/components/parameters/momentId'
      responses:
        '200':
          description: OK
      operationId: gmCmdMomentLotteryDraw
  /gm_cmd/moment_lottery/attend:
    post:
      summary: GM指令-用于测试互动后，是否参与成功
      tags:
        - gm
        - moment_lottery
      security: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/momentId'
        - name: action
          in: query
          required: true
          schema:
            type: string
            enum:
              - like
              - comment
              - forward
              - follow
            example: like
      responses:
        '200':
          description: OK
      operationId: gmCmdMomentLotteryAttend
  /gm_cmd/gms/postReceiveLimit.php:
    post:
      summary: GM指令-抽奖动态发奖指令
      description: |
        手动触发发奖，调用游戏发奖指令, 纯代理，保持上游接口风格
      tags:
        - gm
        - moment_lottery
      security: []
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                cmd:
                  type: string
                  example: drawlotterymoment
                operatorInfo:
                  type: string
                  example: drawlotterymoment
                serverId:
                  type: string
                  example: '51'
                sn:
                  type: string
                  example: JAUMALtlc2cAAAAA
                type:
                  type: number
                  example: 1
                srcPlayerId:
                  type: number
                  example: 681500012
                winners:
                  type: string
                  example: 10328100012,10325000012
                prizes:
                  type: string
                  example: 21000039,1,9
                returnJade:
                  type: number
                  example: 432
                reason:
                  type: string
                  example: ''
              required:
                - cmd
                - operatorInfo
                - serverId
                - sn
                - type
                - srcPlayerId
                - winners
                - prizes
                - returnJade
                - reason
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  result:
                    type: object
                    properties:
                      SN:
                        type: string
                        example: JAUMALtlc2cAAAAA
                    required:
                      - SN
                required:
                  - status
                  - result
      operationId: gmCmdGmsPostreceivelimit.php
  /gm_cmd/qmpk/set_rank:
    post:
      summary: 设置全面争霸排行名次(只在测试环境可用)
      tags:
        - gm
      security: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - name: originRoleId
          in: query
          description: 原始服roleId
          schema:
            type: number
            example: 100100002
        - name: rank
          in: query
          description: 排名(从1计数)
          schema:
            type: number
            example: 1
        - $ref: '#/components/parameters/qmpkDate'
      responses:
        '200':
          description: OK
      operationId: gmCmdQmpkSetRank
  /gm_cmd/hotmoment/refresh:
    post:
      summary: GM指令-热门动态-刷新热门
      description: 更新热门动态缓存, 传入serverIds和tagIds, tagIds和serverIds都可以选择多个
      operationId: gmCmdUpdateHotMomentsCache
      security: []
      tags:
        - gm
        - hotmoment
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                tagIds:
                  type: array
                  items:
                    type: number
                    example: 1
                serverIds:
                  type: array
                  items:
                    type: string
                    description: 服务器id, 用字符串, all代表全服
                    example: all
      responses:
        '200':
          description: OK
  /activity/qmpk/share:
    post:
      summary: 全面PK分享
      tags:
        - qmpk
      parameters:
        - $ref: '#/components/parameters/roleid'
        - name: origin_roleid
          in: query
          description: 原始服角色ID
          schema:
            type: number
            example: 1001
        - $ref: '#/components/parameters/text'
        - $ref: '#/components/parameters/tsOpt'
        - name: url
          in: query
          description: 全民pk分享图URL
          schema:
            type: string
            example: https://nos.netease.com/hi-163-qnm/qnm/mrt2020_img/********/**************.jpg?imageView
      responses:
        '200':
          description: OK
      operationId: activityQmpkShare
  /auth/login:
    post:
      tags:
        - auth
      operationId: authLogin
      summary: 角色登录梦岛
      security: []
      description: 角色登录梦岛, 获取接口访问凭证
      parameters:
        - $ref: '#/components/parameters/authTime'
        - $ref: '#/components/parameters/authAccount'
        - $ref: '#/components/parameters/authUrs'
        - $ref: '#/components/parameters/authAccountId'
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/server'
        - $ref: '#/components/parameters/roleLevel'
        - $ref: '#/components/parameters/language'
        - $ref: '#/components/parameters/country'
        - $ref: '#/components/parameters/authToken'
        - name: isOverSea
          in: query
          description: |
            【废弃】是否为海外用户, 0: 否, 1: 是
            （该字段已废弃，请使用 speechLimitStrategy 字段进行精细化控制）
          deprecated: true
          schema:
            type: integer
            enum:
              - 0
              - 1
            example: true
        - name: speechLimitStrategy
          in: query
          description: |
            发言限制策略，使用二进制标志位控制：
            - 0 (0000): 无限制
            - 1 (0001): 屏蔽发言 (BLOCK_SPEECH)
            - 2 (0010): 屏蔽发图 (BLOCK_IMAGE)
            - 3 (0011): 屏蔽发言和发图 (BLOCK_ALL)
          schema:
            type: string
            enum:
              - '0'
              - '1'
              - '2'
              - '3'
            example: '3'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - data
                properties:
                  code:
                    type: number
                    example: 0
                  data:
                    $ref: '#/components/schemas/auth-login-resp'
  /server/player/setProtectMode:
    post:
      summary: 梦岛设置好友可见
      tags:
        - server
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/time'
        - name: protectMode
          in: query
          description: 是否开启朋友圈仅好友可见
          required: true
          schema:
            type: number
            example: 1
        - name: token
          in: query
          description: md5(roleId + protectMode + time + AUTH_TOKEN_SALT)
          required: true
          schema:
            type: string
            example: stdftfd671fe7612f
      responses:
        '200':
          description: OK
      operationId: serverPlayerSetProtectMode
  /daily_login/login_time:
    get:
      summary: 获取每日登录时间(只支持7d之内)
      tags:
        - daily_login
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/ds'
        - $ref: '#/components/parameters/nonce'
        - $ref: '#/components/parameters/ts'
        - name: token
          required: true
          in: query
          schema:
            type: string
            description: token md5(ds + nonce + roleid + ts + secretKey) 按照Ascii升序的key对应的值md5hash所得
            example: 8947248ed8ee41a51cff29223f25a4c2
      responses:
        '200':
          $ref: '#/components/responses/DailyLoginLoginTimeRes'
      operationId: dailyLoginLoginTime
  /auth/mark_role_status:
    post:
      summary: 标记游戏角色状态
      description: 验证参数和方式同梦岛登录接口
      tags:
        - auth
      parameters:
        - $ref: '#/components/parameters/authTime'
        - $ref: '#/components/parameters/authAccount'
        - $ref: '#/components/parameters/authUrs'
        - $ref: '#/components/parameters/authRoleId'
        - $ref: '#/components/parameters/authToken'
        - $ref: '#/components/parameters/accountStatus'
      responses:
        '200':
          description: OK
      operationId: authMarkRoleStatus
  /activity/get_player_moments:
    get:
      summary: 获取玩家心情列表
      tags:
        - activity
      parameters:
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          description: ok
      operationId: activityGetPlayerMoments
  /activity/survey/submit:
    post:
      summary: 问卷系统回调接口
      tags:
        - misc
      parameters:
        - name: question
          required: true
          in: query
          schema:
            type: string
            example: ''
        - name: url
          required: true
          in: query
          schema:
            type: string
            example: http://www.question.url?qnm_md_info=*********.**********
        - name: serverid
          required: false
          in: query
          schema:
            type: number
            example: 4
      responses:
        '200':
          description: ok
      operationId: activitySurveySubmit
  /getallinforms:
    post:
      summary: 通知-全部列表
      description: 获取全部通知
      tags:
        - inform
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/lastid2'
        - $ref: '#/components/parameters/inform-pagesize'
      responses:
        '200':
          $ref: '#/components/responses/res-inform-list'
  /getinforms:
    post:
      summary: 通知-未读列表
      description: 获取未读通知列表
      tags:
        - inform
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/lastid2'
        - $ref: '#/components/parameters/inform-pagesize'
      responses:
        '200':
          $ref: '#/components/responses/res-inform-list'
  /getnewsnum:
    post:
      summary: 通知-新消息数量
      description: 获取新消息数量
      tags:
        - inform
      parameters:
        - $ref: '#/components/parameters/roleid'
        - in: query
          name: include
          description: 是否返回最新的一条未读消息
          required: false
          schema:
            type: string
            enum:
              - newinform
      responses:
        '200':
          description: ok
          $ref: '#/components/responses/res-inform-news-num'
  /informs/read_all:
    post:
      summary: 通知-标记所有消息为已读
      description: 将指定用户的所有通知标记为已读，并返回标记的消息数量
      tags:
        - inform
      parameters:
        - $ref: '#/components/parameters/roleid'
      responses:
        '200':
          description: 成功标记所有通知为已读
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  data:
                    type: object
                    properties:
                      markedCount:
                        type: integer
                        description: 标记为已读的消息数量
                        example: 10
  /addmessage:
    post:
      description: 添加留言
      tags:
        - message
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/targetid'
        - $ref: '#/components/parameters/replyid'
        - $ref: '#/components/parameters/text'
      responses:
        '200':
          description: OK
      operationId: addmessage
  /getprofile:
    post:
      summary: 获取玩家信息详情
      tags:
        - players
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/targetid'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/getProfile'
      operationId: getprofile
  /clean_account:
    post:
      summary: 清空梦岛所有信息(服务器调用)
      description: 需要删除的内容为：我的梦岛，心愿，留言板，曾用名（曾用名应该是游戏这边的）, token计算方式和 auth/login 相同
      tags:
        - players
      parameters:
        - $ref: '#/components/parameters/authTime'
        - $ref: '#/components/parameters/authTime'
        - $ref: '#/components/parameters/authAccount'
        - $ref: '#/components/parameters/authUrs'
        - $ref: '#/components/parameters/authRoleId'
        - $ref: '#/components/parameters/authToken'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
      operationId: cleanAccount
  /background/syncId:
    post:
      tags:
        - background
      summary: 同步玩家的背景图片(服务端)
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/syncBackgroundReq'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/syncBackgroundRes'
      operationId: backgroundSyncid
  /getevents:
    post:
      summary: 获取互动事件列表
      tags:
        - events
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/targetid'
        - $ref: '#/components/parameters/eventType'
      responses:
        '200':
          description: OK
      operationId: getevents
  /addevent:
    post:
      summary: 添加互动事件
      tags:
        - events
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/targetid'
        - $ref: '#/components/parameters/server'
        - $ref: '#/components/parameters/parameter'
        - $ref: '#/components/parameters/eventType'
        - $ref: '#/components/parameters/eventtoken'
      responses:
        '200':
          description: OK
      operationId: addevent
  /info/get_locations:
    post:
      summary: 获取地理位置(客户端家谱)
      tags:
        - info
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/targetids'
      responses:
        '200':
          description: OK
      operationId: infoGetLocations
  /info/get_avatar:
    post:
      summary: 获取玩家头像
      tags:
        - info
      parameters:
        - $ref: '#/components/parameters/roleid'
      responses:
        '200':
          description: OK
      operationId: infoGetAvatar
  /location/nearby/players:
    post:
      summary: 附近的玩家
      tags:
        - lbs
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/skey'
        - name: longitude
          in: query
          description: 经度
          schema:
            type: number
          example: 120.19175
        - name: latitude
          in: query
          description: 纬度
          schema:
            type: number
          example: 30.187599
        - name: distance
          in: query
          description: 附近的人距离(m)
          schema:
            type: number
          example: 5000
      responses:
        '200':
          description: OK
      operationId: locationNearbyPlayers
  /auth/login_by_ds:
    post:
      summary: 大神web端登录
      tags:
        - auth
      parameters:
        - name: accessToken
          in: query
          description: 大神jssdk返回的token
          schema:
            type: string
        - name: mockRoleId
          in: query
          description: 模拟登陆的roleId， 仅测试环境有效
          schema:
            type: number
      responses:
        '200':
          description: OK
      operationId: authLoginByDs
  /getmoments:
    post:
      summary: 获取用户朋友圈或者指定用户的朋友圈
      tags:
        - moment
      operationId: listMoment
      parameters:
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pagesize'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MomentListRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/MomentGetMomentsRes'
  /moment/add_article:
    post:
      summary: 发表朋友圈长文
      deprecated: true
      tags:
        - discard
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/m_title'
        - $ref: '#/components/parameters/m_content'
        - $ref: '#/components/parameters/imglist'
        - $ref: '#/components/parameters/videolist'
      responses:
        '200':
          description: OK
      operationId: momentAddArticle
  /getmomentbyid:
    post:
      tags:
        - moment
      summary: 动态-通过id获取动态详情
      operationId: getMomentById
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/momentid'
        - $ref: '#/components/parameters/textStyle'
      responses:
        '200':
          $ref: '#/components/responses/MomentGetDetailRes'
  /moment/listByGuild:
    post:
      tags:
        - moment
      summary: 动态-查看本帮
      operationId: listMomentByGuild
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MomentListByGuildReq'
      responses:
        '200':
          description: OK
  /addmoment:
    post:
      tags:
        - moment
      summary: 添加动态
      description: |
        话题特殊文本格式b2例子 `<link button=coser活动,PSHotTalk,2>`
        @玩家功能富文本格式例子  `<link button=太刀川庆,AtPlayer,281851200548>` 代表@281851200548这个玩家
      operationId: addMoment
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MomentAddRequestBody'
      responses:
        '200':
          description: OK
  /moment/forward:
    post:
      tags:
        - moment
      summary: 转发动态
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/momentid'
        - $ref: '#/components/parameters/textRequired'
      responses:
        '200':
          description: OK
      operationId: momentForward
  /delmoment:
    description: ''
    post:
      tags:
        - moment
      summary: 删除动态
      operationId: delMoment
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/momentid'
      responses:
        '200':
          description: OK
  /gethotmoments:
    post:
      tags:
        - hotMoment
      summary: 热门动态-本服热门
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MomentHotListRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/MomentListHotRes'
      operationId: gethotmoments
  /getallhotmoments:
    post:
      tags:
        - hotMoment
      summary: 热门动态-全服热门
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MomentAllServerHotListRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/MomentListHotRes'
      operationId: getallhotmoments
  /comment/more:
    post:
      tags:
        - comment
      summary: 获取更多评论
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/commentid2'
      responses:
        '200':
          description: OK
      operationId: commentMore
  /comment/list:
    get:
      tags:
        - comment
      summary: 评论列表
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/momentId'
        - $ref: '#/components/parameters/lastid'
        - $ref: '#/components/parameters/pagesize'
      responses:
        '200':
          description: OK
      operationId: commentList
  /addcomment:
    post:
      tags:
        - comment
      summary: 添加评论或回复
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/momentid'
        - $ref: '#/components/parameters/text'
        - $ref: '#/components/parameters/replyid'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                properties:
                  code:
                    type: number
                  msg:
                    description: 错误提示信息
                    type: string
                  data:
                    type: object
                    properties:
                      id:
                        type: number
              examples:
                AddOk:
                  value:
                    code: 0
                    data:
                      id: 1
                DuplicateError:
                  value:
                    code: -2003
                    msg: 您不能评论重复内容
      operationId: addcomment
  /delcomment:
    post:
      tags:
        - comment
      summary: 删除评论
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/commentid'
      responses:
        '200':
          description: OK
      operationId: delcomment
  /likemoment:
    post:
      tags:
        - like
      summary: 点赞心情
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/momentid'
        - $ref: '#/components/parameters/likeAction'
      responses:
        '200':
          description: OK
      operationId: likemoment
  /follow/add:
    post:
      tags:
        - follow
      summary: 关注
      description: |
        |ErrorCode| Message|
        |---| ---|
        |-1000 | 到达关注上限|
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/targetid'
      responses:
        '200':
          description: OK
      operationId: followAdd
  /follow/cancel:
    post:
      tags:
        - follow
      summary: 取消关注
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/targetid'
      responses:
        '200':
          description: OK
      operationId: followCancel
  /follow/cancel_batch:
    post:
      tags:
        - follow
      summary: 取消关注
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/cacelTargetIds'
      responses:
        '200':
          description: OK
      operationId: followCancelBatch
  /follow/follow_list:
    post:
      tags:
        - follow
      summary: 关注列表
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          description: OK
      operationId: followFollowList
  /following:
    post:
      tags:
        - follow
      summary: 关注列表
      parameters:
        - $ref: '#/components/parameters/roleid'
      responses:
        '200':
          description: OK
      operationId: following
  /followers:
    post:
      tags:
        - follow
      summary: 粉丝列表
      parameters:
        - $ref: '#/components/parameters/roleid'
      responses:
        '200':
          description: OK
      operationId: followers
  /getlocation:
    post:
      tags:
        - players
      summary: 梦岛个人信息左侧自动获取地理位置依赖的接口
      responses:
        '200':
          $ref: '#/components/responses/GetLocationRes'
      operationId: getlocation
  /common/ip2location:
    get:
      tags:
        - common
      summary: 根据ip查询ip库返回地理位置
      responses:
        '200':
          $ref: '#/components/responses/GetLocationRes'
      operationId: commonIp2location
  /nos/gettoken:
    get:
      tags:
        - common
      summary: 获取nosToken
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/nosType'
        - $ref: '#/components/parameters/nosExtName'
        - $ref: '#/components/parameters/nosStyle'
      responses:
        '200':
          description: OK
      operationId: nosGettoken
  /topic/list:
    post:
      tags:
        - topic
      summary: 活动话题列表
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccRes'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/Topic'
      operationId: topicList
  /getrank:
    post:
      tags:
        - rank
      summary: 获取排行
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/serverid'
        - name: type
          description: |
            | 类型 |      value |
            |----------|:-------------:|
            | 人气 |  1|
            | 鲜花 |  2|
            | 收花 |  3|
            | 送花 |  4|
            | 人气周榜 |  5|
            | 鲜花周榜|  6|
            | 收花周榜 |  7|
            | 送花周榜|  8|
          required: true
          in: query
          schema:
            type: number
            enum:
              - 1
              - 2
              - 3
              - 4
              - 5
              - 6
              - 7
              - 8
            example: 1
      responses:
        '200':
          description: OK
      operationId: getrank
  /topic/moment/list:
    post:
      tags:
        - topic
      summary: 列出话题心情
      parameters:
        - $ref: '#/components/parameters/roleid'
        - name: topicId
          in: query
          schema:
            type: number
            default: 1
        - name: sort
          in: query
          schema:
            type: string
            enum:
              - hot
              - new
              - image
              - follow
            default: hot
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          description: OK
      operationId: topicMomentList
  /topic_admin/add:
    post:
      tags:
        - topic_admin
      summary: 添加话题
      requestBody:
        content:
          application/json:
            schema:
              required:
                - ID
                - Name
                - Banner
                - Url
                - Desc
                - TopTime
                - CreateTime
                - Status
              properties:
                name:
                  type: string
                  example: '#牛图PK大赛#'
                banner:
                  type: string
                  example: http://hi-163-qnm.nosdn.127.net/upload/201902/27/4dce5c803a8211e9ba846b43c7ff77bb.jpg
                url:
                  type: string
                  description: 对应话题对应调整的url
                  example: http://test.nie.163.com/test_html/qnm/dtds/#/center/
                desc:
                  type: string
                  example: 带话题#牛图PK大赛#发布梦岛，晒出你的牛图，有什么装备趣闻让大家开开眼！
                topAt:
                  type: number
                  description: 定时置顶的时间
                  example: 1605519946681
      responses:
        '200':
          description: OK
      operationId: topicAdminAdd
  /topic_admin/update_status:
    post:
      tags:
        - topic_admin
      summary: 删除话题
      parameters:
        - name: id
          in: query
          schema:
            type: number
            example: 1
        - name: status
          in: query
          description: |
            | value  | desc |
            |-------|------|
            | -1    | 下架 |
            | 0     | 发布 |
            | 0     | 置顶 |
          schema:
            type: number
            enum:
              - -1
              - 0
              - 1
            example: 1
      responses:
        '200':
          description: OK
      operationId: topicAdminUpdateStatus
  /topic_admin/list:
    get:
      tags:
        - topic_admin
      summary: 话题列表
      parameters:
        - $ref: '#/components/parameters/roleid'
      responses:
        '200':
          description: OK
      operationId: topicAdminList
  /open/getNosToken/{filename}/{extname}:
    get:
      tags:
        - common
      summary: 获取nosToken(开放接口)
      parameters:
        - name: filename
          in: path
          required: true
          schema:
            type: string
          example: md14e45ab34b
        - name: extname
          in: path
          required: true
          schema:
            type: string
            enum:
              - jpg
              - jpeg
              - png
      responses:
        '200':
          description: OK
      operationId: openGetnostoken
  /fuxi/getNosToken:
    get:
      tags:
        - fuxi
      summary: 获取Nos上传Token
      parameters:
        - name: type
          required: true
          in: query
          schema:
            type: string
            enum:
              - photo_enhance
        - name: extName
          in: query
          required: true
          schema:
            type: string
            enum:
              - jpg
              - jpeg
              - png
          example: png
      responses:
        '200':
          description: OK
      operationId: fuxiGetnostoken
  /gm_cmd/job_avatar/upload:
    post:
      tags:
        - gm
      security: []
      summary: 上传官网职业头像
      parameters:
        - name: gender
          description: 性别
          in: query
          schema:
            type: string
            enum:
              - male
              - female
            example: male
        - name: clazz
          description: 职业
          in: query
          schema:
            $ref: '#/components/schemas/Clazz'
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                avatar:
                  description: 职业头像图片
                  type: string
                  format: binary
      responses:
        '200':
          description: OK
      operationId: gmCmdJobAvatarUpload
  /wishlist/add:
    post:
      tags:
        - wishlist
      summary: 新增心愿
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddWishPayload'
      responses:
        '200':
          description: OK
      operationId: wishlistAdd
  /wishlist/addHelps:
    post:
      tags:
        - wishlist
      summary: 助力心愿单
      requestBody:
        content:
          application/json:
            schema:
              type: object
            example:
              roleid: *********
              targetid: *********
              wishId: 9c3dbc74f33dd3f2
              text: example text
              progress: 100
              history:
                - 10
                - 20
                - 70
      responses:
        '200':
          description: OK
      operationId: wishlistAddhelps
  /wishlist/updateStatus:
    post:
      tags:
        - wishlist
      summary: 修改状态
      description: |
        status:
          ```
          enum EWishStatus {
              Default = 0, //默认，玩家可以助力、删除等操作
            Reward = 1, //已领奖
            Delete = 2, //已删除
            Return = 3, //已返还
          }
          ```
        visibility:
          ```
          enum EVisibility {
              All = 0, //所有人可见
              None = 1, //无人可见
              Owner = 2  //自己可见
          }
          ```
        status与isHide参数至少传递其中一个参数
      requestBody:
        content:
          application/json:
            schema:
              type: object
            example:
              roleid: *********
              wishId: 9c3dbc74f33dd3f2
              status: 0
              visibility: 0
      responses:
        '200':
          description: OK
      operationId: wishlistUpdatestatus
  /wishlist/syncOne:
    post:
      tags:
        - wishlist
      summary: 同步心愿单
      requestBody:
        content:
          application/json:
            schema:
              type: object
            example:
              roleid: *********
              wishlist:
                - roleid: *********
                  wishId: 9c3dbc74f33dd3f2
                  type: 1
                  text: example text
                  templateId: 111
                  regionId: 1
                  num: 11
                  totalProgress: 11
                  startTime: 1599725110252
                  endTime: 1599725110252
                  status: 0
                  helps:
                    - roleid: 111011
                      targetid: *********
                      wishId: 9c3dbc74f33dd3f2
                      progress: 100
                      history:
                        - 10
                        - 20
                        - 70
      responses:
        '200':
          description: OK
      operationId: wishlistSyncone
  /wishlist/listByRole:
    post:
      tags:
        - wishlistClient
      summary: 获取某人心愿单
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/targetid'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          description: OK
      operationId: wishlistListbyrole
  /wishlist/list:
    post:
      tags:
        - wishlistClient
      summary: 获取自己和朋友的心愿单
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          description: OK
      operationId: wishlistList
  /wishlist/detail:
    post:
      tags:
        - wishlistClient
      summary: 查看某条心愿单
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/wishId'
      responses:
        '200':
          description: OK
      operationId: wishlistDetail
  /wishlist/help_text/add:
    post:
      tags:
        - wishlistClient
      summary: 添加文本类心愿助力
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/wishId'
        - $ref: '#/components/parameters/helpText'
      responses:
        '200':
          description: OK
      operationId: wishlistHelpTextAdd
  /wishlist/help_text/del:
    post:
      tags:
        - wishlistClient
      summary: 删除文本类心愿助力
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/helpId'
      responses:
        '200':
          description: OK
      operationId: wishlistHelpTextDel
  /firework_photo/update:
    post:
      tags:
        - firework_photo
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/nosUrl'
        - $ref: '#/components/parameters/index'
      summary: 更新指定位置的烟花图片
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                required:
                  - code
                  - data
                properties:
                  code:
                    type: number
                    example: 0
                  data:
                    required:
                      - id
                    properties:
                      id:
                        type: number
                        example: 1
                    type: object
      operationId: fireworkPhotoUpdate
  /firework_photo/remove:
    post:
      tags:
        - firework_photo
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/index'
      summary: 删除指定位置的图片
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: number
                    example: 0
                  data:
                    type: object
                    properties:
                      id:
                        type: number
                        example: 0
      operationId: fireworkPhotoRemove
  /firework_photo/list:
    get:
      tags:
        - firework_photo
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/targetid'
      summary: 玩家烟花图片列表(10张)
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FireworkPhotoListRes'
              examples:
                fireworksList:
                  $ref: '#/components/examples/fireworksList'
      operationId: fireworkPhotoList
  /firework_photo/get_by_ids:
    get:
      tags:
        - firework_photo_server
      parameters:
        - $ref: '#/components/parameters/roleid'
        - name: ids
          in: query
          description: 分享图片列表(csv格式, 最大四个)
          schema:
            type: string
            example: 1,2
      summary: 通过图片id列表获取图片
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FireworkPhotoListRes'
              examples:
                fireworksList:
                  $ref: '#/components/examples/fireworksList'
      operationId: fireworkPhotoGetByIds
  /home_decorate_photo/update:
    post:
      tags:
        - home_decorate_photo
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/nosUrl'
        - $ref: '#/components/parameters/index'
      summary: 更新指定位置的家园自定义装饰图片
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                required:
                  - code
                  - data
                properties:
                  code:
                    type: number
                    example: 0
                  data:
                    required:
                      - id
                    properties:
                      id:
                        type: number
                        example: 1
                    type: object
      operationId: homeDecoratePhotoUpdate
  /home_decorate_photo/remove:
    post:
      tags:
        - home_decorate_photo
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/index'
      summary: 删除指定位置的图片
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: number
                    example: 0
                  data:
                    type: object
                    properties:
                      id:
                        type: number
                        example: 0
      operationId: homeDecoratePhotoRemove
  /home_decorate_photo/list:
    get:
      tags:
        - home_decorate_photo
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/targetid'
      summary: 玩家庄园自定义图片列表(10张)
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FireworkPhotoListRes'
              examples:
                fireworksList:
                  $ref: '#/components/examples/fireworksList'
      operationId: homeDecoratePhotoList
  /home_decorate_photo/get_by_ids:
    get:
      tags:
        - home_decorate_photo
      parameters:
        - $ref: '#/components/parameters/roleid'
        - name: ids
          in: query
          description: 分享图片列表(csv格式, 最大四个)
          schema:
            type: string
            example: 1,2
      summary: 通过图片id列表获取图片
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FireworkPhotoListRes'
              examples:
                fireworksList:
                  $ref: '#/components/examples/fireworksList'
      operationId: homeDecoratePhotoGetByIds
  /report_log/get_nos_token:
    get:
      tags:
        - report_log
      parameters:
        - name: objectname
          in: query
          required: true
          schema:
            type: string
            example: role_id_202012_error.log
        - $ref: '#/components/parameters/deviceId'
      operationId: getReportLogNosToken
      summary: 获取上传玩家日志文件NosToken
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - data
                properties:
                  code:
                    type: number
                    example: 0
                  data:
                    $ref: '#/components/schemas/NosToken'
  /report_log/add:
    post:
      tags:
        - report_log
      operationId: addReportLog
      summary: 添加设备汇报日志文件
      parameters:
        - $ref: '#/components/parameters/deviceId'
        - name: url
          in: query
          required: true
          schema:
            type: string
            example: http://device_example.log
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - data
                properties:
                  code:
                    type: number
                    example: 0
                  data:
                    type: object
                    properties:
                      id:
                        type: number
                        example: 1
  /report_log/list:
    get:
      tags:
        - report_log
      operationId: listReportLog
      summary: 玩家日志文件列表检索
      parameters:
        - name: start_time
          description: 开始时间 单位(ms)
          in: query
          schema:
            type: number
            example: 1617179813989
        - name: end_time
          description: 结束时间 单位(ms)
          in: query
          schema:
            type: number
            example: 1817179813989
        - $ref: '#/components/parameters/deviceId'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - data
                properties:
                  code:
                    type: number
                    example: 0
                  data:
                    type: object
                    properties:
                      list:
                        type: array
                        items:
                          $ref: '#/components/schemas/ReportLogItem'
                      meta:
                        $ref: '#/components/schemas/PaginationMeta'
  /report_log/allow_device/list:
    get:
      tags:
        - report_log
      summary: 列出允许设备名单列表
      operationId: listAllowDevice
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - data
                properties:
                  code:
                    type: number
                    example: 0
                  data:
                    type: object
                    properties:
                      deviceIds:
                        type: array
                        items:
                          type: string
                          example: device_id_example_1
  /report_log/allow_device/add:
    post:
      tags:
        - report_log
      operationId: addAllowDevice
      summary: 添加设备到允许名单
      parameters:
        - $ref: '#/components/parameters/deviceId'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - data
                properties:
                  code:
                    type: number
                    example: 0
                  data:
                    type: object
                    properties:
                      len:
                        type: number
                        example: 1
  /report_log/allow_device/remove:
    post:
      tags:
        - report_log
      summary: 从允许名单中删除设备
      operationId: removeAllowDevice
      parameters:
        - $ref: '#/components/parameters/deviceId'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - data
                properties:
                  code:
                    type: number
                    example: 0
                  data:
                    type: object
                    properties:
                      len:
                        type: number
                        example: 1
  /server/moment_lottery/add:
    post:
      tags:
        - server
        - moment_lottery
      summary: 动态抽奖-添加
      description: 动态抽奖-添加, **游戏服务器调用**
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ServerMomentLotteryAddReq'
      responses:
        '200':
          $ref: '#/components/responses/ServerMomentLotteryAddRes'
      operationId: serverMomentLotteryAdd
  /moment_lottery/show:
    get:
      tags:
        - moment_lottery
      summary: 动态抽奖-展示
      description: 动态抽奖-展示, 用于包含动态抽奖的动态展示当前的抽奖信息
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/momentId'
      responses:
        '200':
          $ref: '#/components/responses/res-moment-lottery-show'
      operationId: momentLotteryShow
  /moment_lottery/winners:
    get:
      tags:
        - moment_lottery
      summary: 动态抽奖-中奖玩家名单
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/momentId'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          $ref: '#/components/responses/MomentLotteryWinnersRes'
      operationId: momentLotteryWinners
  /server/transfer/add:
    post:
      tags:
        - server
        - transfer
      summary: 添加转服记录, 并完成所有RoleId相关数据的迁移
      parameters:
        - name: oldId
          description: 转服前的ID
          in: query
          schema:
            type: number
            example: 24017600001
        - name: newId
          in: query
          description: 转服后的ID
          schema:
            type: number
            example: 24017600002
        - $ref: '#/components/parameters/time'
        - $ref: '#/components/parameters/nonce'
        - name: token
          in: query
          description: token计算方式为 md5(ASCII升序参数的值 + salt),  即为 md5(newId + nonce + oldId + time + auth_slat)
          schema:
            type: string
            example: 37ad3c65836e4154f22f890b57fe82f2
      responses:
        '200':
          $ref: '#/components/responses/ServerTransferAddRes'
      operationId: serverTransferAdd
  /server/fpFileReviewCallback:
    post:
      tags:
        - server
      summary: filePicker文件审核回调
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - project_name
                - operator
                - files
              properties:
                project_name:
                  type: string
                  description: 项目名
                  example: test
                operator:
                  type: string
                  description: 操作者
                  example: <EMAIL>
                files:
                  type: array
                  items:
                    type: object
                    properties:
                      file_id:
                        type: string
                        description: 文件id
                        example: 625f7221173f910c0fdb544b0HfIAxYq02
                      from_status:
                        type: integer
                        description: 文件原始状态
                        example: 1
                      to_status:
                        type: integer
                        description: 审核后状态
                        example: 3
                      extra_info:
                        type: object
                        description: 文件的extraInfo信息
                        properties:
                          review_callback_url:
                            type: string
                            description: 审核回调地址
                            example: http://example.com/callback
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
      operationId: serverFpFileReviewCallback
components:
  schemas:
    MomentLotteryWinner:
      type: object
      required:
        - roleId
        - roleName
        - serverId
        - serverName
      properties:
        roleId:
          type: number
          example: 24017600001
        roleName:
          type: string
          example: 中奖的玩家名
        serverId:
          type: number
          example: 1
        serverName:
          type: string
          example: 服务器名
    MomentLotteryWinnersData:
      type: object
      required:
        - momentId
        - winners
        - winnerNum
        - hostPlayer
        - drawTime
        - drawStatus
      properties:
        momentId:
          type: number
          example: 15938804987
        winners:
          type: array
          items:
            $ref: '#/components/schemas/MomentLotteryWinner'
        winnerNum:
          type: integer
          description: 中奖人数
          example: 100
        hostPlayer:
          type: object
          required:
            - roleId
            - roleName
            - serverId
            - serverName
          properties:
            roleId:
              type: number
              example: 24017600001
            roleName:
              type: string
              example: 发起抽奖的玩家名
            serverId:
              type: number
              example: 1
            serverName:
              type: string
              example: 服务器名
        drawTime:
          type: integer
          description: 开奖时间(ms)
        drawStatus:
          type: integer
          description: 抽奖状态 0 未开奖 1 已开奖 2 已取消
          example: 1
    ServerMomentLotteryAddResp:
      type: object
      properties:
        id:
          type: number
          description: 动态id
          example: 1
        lotterySn:
          type: string
          description: 抽奖动态的唯一id
          example: lottery_sn
    DailyLoginLoginTime:
      type: object
      properties:
        roleid:
          type: number
          description: 角色id
          example: *********
        loginTime:
          type: number
          description: 当日最新一次登录时间
          example: 1723639944885
    MomentLotteryPrize:
      type: string
      description: 动态抽奖礼品(是指单份，就是中奖者能获取的奖励), 格式为 <道具id>,<数量>,<价格>;<道具id>,<数量>,<价格>
      example: 21000039,1,9;21000040,2,30
    MomentLotteryAdd:
      type: object
      properties:
        sn:
          type: string
          description: 每条抽奖动态的唯一id, 用于保证幂等
        type:
          type: integer
          description: 抽奖类型 1:阳光普照 2:天选之人
          enum:
            - 1
            - 2
          example: 1
        prizes:
          $ref: '#/components/schemas/MomentLotteryPrize'
        jade:
          type: integer
          description: 所需的灵玉数量
        winnerNum:
          type: integer
          description: 中奖人数
          example: 10
        drawTime:
          type: integer
          description: 开奖时间, 单位秒
        serverScope:
          type: string
          description: 参与抽奖范围, 全服或者本服
          enum:
            - all
            - local
        requirements:
          $ref: '#/components/schemas/moment-lottery-requirements'
        minLevel:
          type: integer
          description: 参与抽奖的最小参与等级, 0代表不开启等级下限
          default: 0
      required:
        - sn
        - type
        - prizes
        - jade
        - drawTime
        - serverScope
        - requirements
        - minLevel
    ServerMomentLotteryAddReq:
      allOf:
        - $ref: '#/components/schemas/MomentAddRequestBody'
        - type: object
          properties:
            lottery:
              $ref: '#/components/schemas/MomentLotteryAdd'
          required:
            - lottery
        - type: object
          required:
            - time
            - token
          properties:
            time:
              type: number
              description: 时间参数, 单位秒
              example: 1599725110
            token:
              type: string
              description: md5(time+roleid+sn+salt)
              example: 086779eda50242b2af78d79e5991ca78
    ServerTransferAddData:
      type: object
      properties:
        taskId:
          type: number
          description: 转服任务id (提供查询转服任务进度)
          example: 1
    MomentTagList:
      required:
        - list
      properties:
        list:
          type: array
          items:
            $ref: '#/components/schemas/MomentTagItem'
    AtPlayersList:
      type: object
      required:
        - list
        - count
      properties:
        list:
          type: array
          items:
            $ref: '#/components/schemas/AtPlayersItem'
        count:
          type: number
          example: 50
    MomentTagItem:
      type: object
      required:
        - id
        - name
      properties:
        id:
          type: number
          example: 1
        name:
          type: string
          description: 标签名
          example: 家园
    AtPlayersItem:
      type: object
      required:
        - roleId
        - roleName
        - serverId
        - level
        - jobId
        - gender
      properties:
        roleId:
          type: number
          example: 42448100244
        roleName:
          type: string
          description: 角色名
          example: 天天不猜
        serverId:
          type: integer
          example: 2
        level:
          type: integer
          example: 10
        jobId:
          type: integer
          example: 10
        gender:
          type: integer
          example: 0
    FashionLotteryList:
      type: object
      required:
        - list
        - count
      properties:
        list:
          type: array
          items:
            $ref: '#/components/schemas/FashionLottery'
        count:
          type: number
          example: 50
    FashionLottery:
      type: object
      required:
        - id
        - roleId
        - itemLevel
        - itemIndex
        - receiveTime
      properties:
        id:
          type: number
          example: 1
        roleId:
          type: number
          example: 42448100244
        itemLevel:
          type: number
          description: 奖品等级,  0 => 精致，1 => 豪华，2 =>至尊
          example: 1
        itemIndex:
          type: number
          description: 获奖编号
          example: 3
        receiveTime:
          type: number
          example: 1617179813989
    FacePinchGetFpToken:
      type: object
      required:
        - token
        - expires
        - project
        - uploadUrl
      properties:
        token:
          type: string
          example: Policy F0nfg+CY6PrcVQ9rV5ZwGuWJMnw=:eyJmc2l6ZUxpbWl0IjpbMCwyMDk3MTUyMF0sImh0dHBzIjp0cnVlLCJtZXRob2QiOiJQT1NUIiwibWltZUxpbWl0IjpbImltYWdlLyoiLCJ2aWRlby8qIiwiYXVkaXQvKiJdLCJyZXZpZXciOjAsInRpbWVzdGFtcCI6MTY5MDE5MTYzNywidXJsIjoiaHR0cHM6Ly9mcC5wcy5uZXRlYXNlLmNvbS9sMTAtbWQtY24vZmlsZS9uZXcvIn0=
        expires:
          type: number
          example: 1690191637
        project:
          type: string
          example: l10-md-cn
        uploadUrl:
          type: string
          example: https://fp.ps.netease.com/l10-md-cn/file/new/
    FacePinchWorkItem:
      allOf:
        - type: object
          required:
            - roleId
            - userId
          properties:
            roleId:
              type: number
              example: 42448100244
            userId:
              type: number
              example: 2
            availableApplyParts:
              type: array
              description: 可用的应用部位名称数组（考虑历史数据限制）
              items:
                type: string
                enum:
                  - Shape
                  - Makeup
                  - FullFace
              example:
                - Shape
                - Makeup
        - $ref: '#/components/schemas/FacePinchWorkAddRet'
        - $ref: '#/components/schemas/FacePinchWorkAdd'
        - $ref: '#/components/schemas/FacePinchWorkStat'
        - $ref: '#/components/schemas/FacePinchUserActionFlag'
    FacePinchWorkCollectUsage:
      type: object
      description: 收藏配额使用量 每个角色100个
      required:
        - max
        - current
      properties:
        max:
          type: number
          description: 最大允许收藏
          example: 100
        current:
          type: number
          description: 当前收藏量
          example: 10
    FacePinchWorkUploadUsage:
      type: object
      description: 设计作品上传配额使用量 每个角色100个
      required:
        - max
        - current
      properties:
        max:
          type: number
          description: 最大允许上传作品
          example: 100
        current:
          type: number
          description: 当前作品数量
          example: 10
    FacePinchWorkExtraForAccuse:
      type: object
      description: 被举报角色相关信息
      required:
        - accusedRole
      properties:
        accusedRole:
          type: object
          required:
            - userId
            - roleId
            - roleName
            - roleLevel
            - server
            - vip
          properties:
            userId:
              type: number
              description: 被举报的捏脸站用户id
              example: 3022
            roleId:
              type: number
              description: 被举报的角色id
              example: 42448100244
            roleName:
              type: string
              description: 被举报的角色名
              example: 冰粉不凉
            roleLevel:
              type: number
              description: 被举报的角色等级
              example: 30
            server:
              type: number
              description: 被举报的服务器
              example: 15
            vip:
              type: number
              description: 被举报的vip登记
              example: 1
        availableApplyParts:
          type: array
          description: 可用的应用部位名称数组（考虑历史数据限制）
          items:
            type: string
            enum:
              - Shape
              - Makeup
              - Fullface
          example:
            - Shape
            - Makeup
    FacePinchWorkAddRetTiny:
      type: object
      description: 添加捏脸作品
      required:
        - id
        - shareId
        - createTime
      properties:
        id:
          type: number
          example: 1
        shareId:
          type: string
          description: 用于生成分享的二维码的链接的分享id
          example: EB446F
        createTime:
          type: number
          example: 1624808747493
    FacePinchWorkAddRet:
      allOf:
        - $ref: '#/components/schemas/FacePinchWorkAddRetTiny'
        - $ref: '#/components/schemas/FacePinchWorkStat'
    FacePinchShareVideoAdd:
      type: object
      properties:
        id:
          type: number
          example: 3
        video:
          type: string
          description: 分享视频（游戏内生成的视频链接）
          example: http://hi-163-qnm.nosdn.127.net/face_pinch/image/ddcc58b01bc811eeb3b69f53439eb902
        shareId:
          type: string
          description: 分享视频生成的分享id
          example: uOh7I_nKZmj9w6wHLP5fj
    FacePinchUserActionFlag:
      type: object
      description: 作品相关统计值
      required:
        - isLiked
        - isCollected
        - onceApplied
      properties:
        isLiked:
          type: boolean
          description: 是否已收藏
          example: false
        isCollected:
          type: boolean
          description: 是否已收藏
          example: false
        onceApplied:
          type: boolean
          description: 是否曾经应用过
          example: false
    FacePinchWorkStat:
      type: object
      description: 作品相关统计值
      required:
        - likeCount
        - collectCount
        - applyCount
        - hot
      properties:
        hot:
          type: number
          description: 总热度
          example: 0
        likeCount:
          type: number
          description: 被点赞计数
          example: 0
        collectCount:
          type: number
          description: 被收藏计数
          example: 0
        applyCount:
          type: number
          description: 被使用创角的计数
          example: 0
    FacePinchWorkAdd:
      type: object
      description: 添加捏脸作品
      required:
        - dataUrl
        - image
        - title
        - roleName
        - gender
        - jobId
        - allowedParts
      properties:
        dataUrl:
          type: string
          description: 捏脸数据对应的nos连接
          example: http://hi-163-qnm.nosdn.127.net/face_pinch/ddcc58b01bc811eeb3b69f53439eb902
        image:
          type: string
          description: 作品大图（游戏内直接传来的一张大图）
          example: http://hi-163-qnm.nosdn.127.net/face_pinch/image/ddcc58b01bc811eeb3b69f53439eb902
        video:
          type: string
          description: 分享视频（游戏内生成的视频链接）
          example: http://hi-163-qnm.nosdn.127.net/face_pinch/image/ddcc58b01bc811eeb3b69f53439eb902
        title:
          type: string
          description: 作品名字
          maxLength: 7
          minLength: 1
          example: 作品名称7个字
        desc:
          type: string
          description: 作品描述
          maxLength: 20
          example: 作品介绍
        roleName:
          type: string
          description: 作者名称, 预创建阶段请使用随机昵称
          example: 雪花里没雪
        gender:
          type: number
          description: 作品所能应用性别
          example: 1
        visibility:
          type: number
          description: 公开级别， 0 => 公开 1 => 私有
          enum:
            - 0
            - 1
          example: 0
        workType:
          type: number
          description: 作品类型 0 => 标准,  1 => 不出现在作品列表中, 只是为了生成二维码数据
          enum:
            - 0
            - 1
          example: 0
        bodyShape:
          $ref: '#/components/schemas/bodyShape'
        jobId:
          type: number
          description: 作品所能应用职业id
          example: 2
        allowedParts:
          type: number
          description: |
            筛选组合及对应数值如下:
            | 组合               | 十进制 | 二进制 |
            |-------------------|--------|--------|
            | 仅塑形            | 1      | 001    |
            | 仅妆容            | 2      | 010    |
            | 塑形+妆容         | 3      | 011    |
            | 仅全脸            | 4      | 100    |
            | 塑形+全脸         | 5      | 101    |
            | 妆容+全脸         | 6      | 110    |
            | 塑形+妆容+全脸     | 7      | 111    |
          minimum: 1
          maximum: 7
          default: 7
          example: 7
    bodyShape:
      type: number
      description: |
        |key|desc|
        |--|--|
        |0|成年男性|
        |1|成年女性|
        |2|壮男|
        |3|萝莉|
      enum:
        - 0
        - 1
        - 2
        - 3
      example: 0
    ExpressionBase:
      type: object
      properties:
        expression:
          type: number
          example: 0
        expression_txt:
          type: number
          example: 0
        sticker:
          type: number
          example: 0
        frame:
          type: number
          example: 0
    ThisDayThatYearHasMomentData:
      type: object
      required:
        - hasMoment
      properties:
        hasMoment:
          type: boolean
          description: 是否发表过
          example: true
    MomentListHotData:
      type: object
      required:
        - list
        - count
      properties:
        list:
          type: array
          items:
            $ref: '#/components/schemas/MomentListItemShow'
        count:
          type: number
          description: 总数量
          example: 5
    ThisDayThatYearMomentListData:
      type: object
      required:
        - list
        - count
      properties:
        list:
          type: array
          items:
            $ref: '#/components/schemas/MomentListItemShow'
        count:
          type: number
          description: 总数量
          example: 5
    MomentListItemShow:
      type: object
      required:
        - id
        - roleid
        - text
        - imglist
        - createtime
        - status
        - is_user_top
        - videolist
        - zancount
        - commentcount
        - is_user_liked
        - zanlist
        - commentable
        - commentlist
        - forwardcount
        - forwardmoment
        - previousforwards
        - isfollowing
        - rolename
        - server_id
        - server_name
        - grade
        - gender
        - clazz
        - expression_base
        - photo
        - used_name
        - splevel
        - xianfanstatus
        - lianghao
        - has_lottery
      properties:
        id:
          type: number
          example: 15938816530
        roleid:
          type: number
          example: *********
        text:
          type: string
          example: ''
        imglist:
          type: array
          items:
            type: object
            required:
              - pic
              - thumb
            properties:
              pic:
                type: string
                example: http://hi-163-qnm.nosdn.127.net/moment/202112/22/9a0cfc4062f311ecb24bedc4ad7046b7?watermark&type=1&gravity=center&image=Y29tbW9uL3dhdGVybWFyay1hdWRpdC5wbmc=
              thumb:
                type: string
                example: http://hi-163-qnm.nosdn.127.net/moment/202112/22/9a0cfc4062f311ecb24bedc4ad7046b7?watermark&type=1&gravity=center&image=Y29tbW9uL3dhdGVybWFyay1hdWRpdC5wbmc=%7cimageView&thumbnail=250y160
        createtime:
          type: number
          example: 1640155894489
        status:
          type: number
          example: 0
        is_user_top:
          type: boolean
          example: false
        videolist:
          type: array
        zancount:
          type: number
          example: 0
        commentcount:
          type: number
          example: 0
        is_user_liked:
          type: boolean
          example: false
        zanlist:
          type: array
        commentable:
          type: boolean
          example: true
        commentlist:
          type: array
        forwardcount:
          type: number
          example: 0
        forwardmoment:
          type: object
          example: null
        previousforwards:
          type: array
        isfollowing:
          type: boolean
          example: false
        rolename:
          type: string
          example: 琳琅安澜
        server_id:
          type: number
          example: 1
        server_name:
          type: string
          example: ''
        grade:
          type: number
          example: 68
        gender:
          type: number
          example: 0
        clazz:
          type: number
          example: 1
        expression_base:
          type: string
          example: '123'
        photo:
          type: string
          example: https://hi-163-qnm.nosdn.127.net/photo/202112/27/85295a3066e011eca0c375c49db0fb34
        used_name:
          type: array
        splevel:
          type: number
          example: 0
        xianfanstatus:
          type: number
          example: 0
        lianghao:
          type: object
          required:
            - id
            - expiredtime
            - hideicon
            - showdigit
          properties:
            id:
              type: number
              example: 0
            expiredtime:
              type: number
              example: 0
            hideicon:
              type: number
              example: 0
            showdigit:
              type: number
              example: 0
        has_lottery:
          type: boolean
          description: 是否包含抽奖
          example: false
        lottery:
          description: 抽奖信息, 只在包含抽奖的动态返回
          $ref: '#/components/schemas/moment-list-item-show-lottery'
    RecentLikeUser:
      type: object
      required:
        - roleId
        - roleName
      properties:
        roleId:
          type: number
          example: 42448100244
        roleName:
          type: string
          example: 小龍男
    ReplyComment:
      type: object
      required:
        - id
        - roleId
        - replyId
        - cardId
        - text
        - createTime
        - roleName
        - clazz
        - gender
      properties:
        id:
          type: number
          example: 1143589
        roleId:
          type: number
          example: 42448100244
        replyId:
          type: string
          example: null
        cardId:
          type: number
          example: 3046
        text:
          type: string
          example: 介绍一下这个支线的历史背景，里面段誉也说了，高氏是因为帮助过大理段氏建立基业所以位极人臣
        createTime:
          type: number
          example: 1624808747493
        roleName:
          type: string
          example: 小龍男
        clazz:
          type: number
          example: 7
        gender:
          type: number
          example: 0
    CardLikeItem:
      type: object
      required:
        - id
        - cardId
        - createTime
      properties:
        id:
          type: number
          example: 1034
        cardId:
          type: number
          example: 2127
        createTime:
          type: number
          example: 1608709903443
    RoleInfoShowTiny:
      type: object
      required:
        - roleId
        - roleName
        - clazz
        - gender
        - grade
        - xianfanstatus
      properties:
        roleId:
          type: number
          description: 评论玩家角色id
          example: 1487200202
        roleName:
          type: string
          description: 角色名
          example: 凤凰羽
        clazz:
          type: number
          description: 职业
          example: 6
        grade:
          type: number
          description: 等级
          example: 60
        gender:
          type: number
          description: 性别
          example: 0
        xianfanstatus:
          type: number
          description: 仙凡状态
          example: 1
    CardNotionItemDetailShow:
      allOf:
        - $ref: '#/components/schemas/CardNotionItemShow'
        - $ref: '#/components/schemas/CardNotionItemDetailExtra'
    CardNotionItemDetailExtra:
      type: object
      required:
        - likeUsers
      properties:
        likeUsers:
          type: array
          items:
            $ref: '#/components/schemas/RecentLikeUser'
        card:
          $ref: '#/components/schemas/CardInfo'
    CardNotionItemShow:
      allOf:
        - $ref: '#/components/schemas/CardNotionItem'
        - $ref: '#/components/schemas/RoleInfoShowTiny'
    CardNotionCommentShow:
      allOf:
        - $ref: '#/components/schemas/CardNotionCommentItem'
        - $ref: '#/components/schemas/RoleInfoShowTiny'
        - $ref: '#/components/schemas/CardNotionCommentReplayItem'
    CardNotificationShow:
      allOf:
        - type: object
          required:
            - notifier
          properties:
            notifier:
              $ref: '#/components/schemas/RoleInfoShowTiny'
        - $ref: '#/components/schemas/CardNotificationItem'
    CardNotionCommentReplayItem:
      type: object
      required:
        - replyId
        - replyRoleId
        - replyRoleName
      properties:
        replyId:
          type: number
          description: 回复的评论id
          example: 10
        replyRoleId:
          type: number
          description: 回复的玩家id
          example: 501034
        replyRoleName:
          type: string
          description: 回复的玩家名字
          example: 玩家名字七个字
    CardNotificationItem:
      type: object
      required:
        - id
        - type
        - text
        - notion
        - createTime
      properties:
        id:
          type: number
          description: 想法id
          example: 501034
        type:
          type: number
          description: 通知类型 1 => 想法被点赞  2 => 想法被评论  3 => 想法的评论被回复
          enum:
            - 1
            - 2
            - 3
          example: 1
        text:
          type: string
          description: 通知的文本， 1的时候为空， 如果通知是2， 那就是评论的文本， 如果是3， 那就回复的文本
          example: 评论消息评论消息
        notion:
          type: object
          description: 通知类型为1, 2的时候，该字段存在
          properties:
            id:
              type: number
              description: 想法id
              example: 501034
            text:
              type: string
              description: 想法文本
              example: 讲道理，这段剧情真的很棒
        notionComment:
          type: object
          description: 通知类型为3的时候，该字段存在
          properties:
            id:
              type: number
              description: 评论id
              example: 501034
            text:
              type: string
              description: 评论文本
              example: emmm, 讲道理这段剧情真的很棒! 场景也美
        createTime:
          type: number
          example: 1529722981979
    CardNotionCommentItem:
      type: object
      required:
        - id
        - cardId
        - notionId
        - text
        - createTime
      properties:
        id:
          type: number
          description: 评论id
          example: 501034
        cardId:
          type: number
          description: 卡片id
          example: 2723
        notionId:
          type: number
          description: 想法id
          example: 2723
        text:
          type: string
          description: 评论文本
          example: 物是人非事事休，欲语泪先流。。。。我本意助其三人快意洒脱笑余生
        createTime:
          type: number
          example: 1529722981979
    CardNotionItem:
      type: object
      required:
        - id
        - cardId
        - text
        - hot
        - createTime
        - likeCount
        - commentCount
        - isLiked
      properties:
        id:
          type: number
          description: 想法id
          example: 501034
        cardId:
          type: number
          description: 卡片id
          example: 2723
        text:
          type: string
          description: 评论文本
          example: 物是人非事事休，欲语泪先流。。。。我本意助其三人快意洒脱笑余生
        hot:
          type: number
          description: 评论热度
          example: 587611
        createTime:
          type: number
          example: 1529722981979
        likeCount:
          type: number
          description: 点赞数
          example: 58675
        commentCount:
          type: number
          description: 二级评论数
          example: 1000
        isLiked:
          type: boolean
          description: 是否点赞过
          example: false
    ApiOkRes:
      type: object
      properties:
        code:
          type: integer
          description: 返回值,0正常, 其他皆为异常
          example: 0
        data:
          type: object
          description: 具体返回的数据对象
    ApiArrOkRes:
      type: object
      properties:
        code:
          type: integer
          description: 返回值,0正常, 其他皆为异常
          example: 0
        data:
          type: array
          description: 具体返回的数据对象
    CardInfo:
      type: object
      required:
        - cardId
        - likeCount
        - isLiked
      properties:
        cardId:
          description: 卡片id
          type: number
          example: 10
        likeCount:
          description: 点赞数
          type: number
          example: 10
        isLiked:
          type: boolean
          description: 是否点赞
          example: false
    ESlientLoveStatus:
      type: number
      description: |
        | id  | desc           |
        | --- | ------         |
        | 0   | 无任何暗恋状态 |
        | 1   | 暗恋中         |
        | 2   | 互相暗恋       |
      enum:
        - 0
        - 1
        - 2
      example: 1
    EquipListItem:
      type: object
      required:
        - Rank
        - RoleId
        - EquipId
        - EquipName
        - EquipIcon
        - Score
        - PositionId
        - RoleName
        - Avatar
        - EquipDetail
      properties:
        Rank:
          type: number
          example: 1
        RoleId:
          type: number
          example: 600002
        EquipId:
          type: string
          example: '2'
        EquipIcon:
          type: string
          example: http://hi-163-qnm.nosdn.127.net/asserts/icons/128x128/equip/23000000.jpg
        EquipName:
          type: string
          example: 轻罗扇
        Score:
          type: number
          example: 100
        PositionId:
          type: number
          example: 1
        RoleName:
          type: string
          example: 繁花诱惑
        Avatar:
          type: string
          example: http://hi-163-qnm.nosdn.127.net/avatars/job_7_female.jpg
        EquipDetail:
          type: object
          required:
            - small
            - desc
            - color
          properties:
            small:
              type: string
              example: http://res.qnm.netease.com/xt/icon/Item_Equipment/Wuqi/Icon/wp_031_01-b.jpg
            desc:
              type: string
              example: <img src="http://res.qnm.netease.com/xt/icon/Item_Equipment/Wuqi/Icon/wp_031_01-b.jpg" /><br/><span style="color:#519FFF">轻罗扇</span><br/><span>琴扇</span><span class="fr">1</span><br/>装备评分 110<br/><b class="entry">基础属性</b><br/> <span style="color:#FFFFFF">物理攻击 27-72</span><span style="color:#00FFFF"></span><br/> <span style="color:#FFFFFF">法术攻击 36-87</span><span style="color:#00FFFF"></span><br/> <span style="color:#FFFFFF">攻击速度 1.237</span><br/><b class="entry">升级完美度</b> <br/><b class="entry">耐久度</b> 80/80<br/><b class="entry">词条属性</b><br/> <span style="color:#519FFF">【坚脆】不可修复（基础属性+50%）</span><br/> <span style="color:#519FFF">【透微】法术命中 +2</span><br/> <span style="color:#519FFF">【惊帆】敏捷 +2</span><br/>
            color:
              type: string
              example: blue
    EquipWeaponsRankData:
      type: object
      required:
        - list
        - updateTime
        - pagination
      properties:
        list:
          type: array
          items:
            $ref: '#/components/schemas/EquipListItem'
        updateTime:
          type: string
          example: '2018-05-09 13:09:44'
        pagination:
          type: object
          required:
            - cur
            - total
          properties:
            cur:
              type: number
              example: 1
            total:
              type: number
              example: 1
    ComplainAddData:
      type: object
      required:
        - id
      properties:
        id:
          type: string
          description: 举报记录id
          example: b037761c50e711ecbf630242ac130002
    EquipCommentListData:
      type: object
      required:
        - list
      properties:
        list:
          type: array
          items:
            type: string
            example: 武器好帅
    WeaponFilterData:
      type: object
      required:
        - servers
        - equipPositions
      properties:
        servers:
          type: object
          description: key为游戏内服务器大区名字
          example: '{"盘古谣": [{ "id": 20, "name": "仙剑问情" }, { "id": 21, "name": "兰若开天" }]}'
          additionalProperties:
            type: object
            required:
              - id
              - name
            properties:
              id:
                type: number
                example: 20
              name:
                type: string
                example: 仙剑问情
        equipPositions:
          type: array
          items:
            type: object
            required:
              - id
              - name
            properties:
              id:
                type: number
                example: 1
              name:
                type: string
                example: 刀剑
    SlientLoveAdd:
      type: object
      required:
        - id
        - status
      properties:
        id:
          description: 新增关系记录id
          type: number
          example: 1
        status:
          $ref: '#/components/schemas/ESlientLoveStatus'
    CardNotionAdd:
      type: object
      required:
        - id
      properties:
        id:
          description: 新增卡片想法id
          type: number
          example: 1
    CommonLikeAction:
      type: object
      required:
        - id
      properties:
        id:
          description: 点赞id
          type: number
          example: 1
    CardNotionLikeAction:
      type: object
      required:
        - id
      properties:
        id:
          description: 卡片想法点赞id
          type: number
          example: 1
    getProfile:
      required:
        - code
        - data
      properties:
        code:
          type: number
          example: 0
        data:
          required:
            - roleid
            - location
            - signature
            - signaturevoice
            - photo
            - showphoto
            - photoaudit
            - privacy
            - banstate
            - friendlist
            - taglist
            - renqi
            - gift
            - flower
            - flowerrenqi
            - sendflowerrenqi
            - shifu
            - tudi
            - wedding
            - updatetime
            - expression_base
            - expression_extra
            - lastloginip
            - lianghao
            - rolename
            - server_id
            - gender
            - grade
            - clazz
            - titleid
            - title
            - gangid
            - gang
            - used_name
            - fightingcapacity
            - usednamestatus
            - xianfanstatus
            - server_name
            - guild
            - hideusedname
            - isfollowing
            - background
            - slientLove
          properties:
            roleid:
              type: number
              example: *********
            location:
              type: string
              example: 大师傅
            signature:
              type: string
              example: 近近景近景123123123
            signaturevoice:
              required:
                - time
                - url
              properties:
                time:
                  type: string
                  example: ''
                url:
                  type: string
                  example: ''
              type: object
            showphoto:
              type: number
              example: 1
            photoaudit:
              type: number
              example: 1
            privacy:
              required:
                - location
                - space
                - name
                - msg_for_following
                - hide_lbs
              properties:
                location:
                  type: boolean
                  example: false
                space:
                  type: boolean
                  example: true
                name:
                  type: boolean
                  example: false
                msg_for_following:
                  type: boolean
                  example: true
                hide_lbs:
                  type: boolean
                  example: true
              type: object
            friendlist:
              type: string
              example: 100200001,100400001
            renqi:
              type: number
              example: 15060
            gift:
              type: number
              example: 2000
            flower:
              type: number
              example: 73
            flowerrenqi:
              type: number
              example: 15059
            sendflowerrenqi:
              type: number
              example: 30758
            wedding:
              required:
                - weddingservername
                - weddingtime
                - weddingindex
                - partner
                - self
              properties:
                weddingtime:
                  type: number
                  example: 0
                weddingindex:
                  type: number
                  example: 0
                partner:
                  required:
                    - id
                    - clazz
                    - name
                    - gender
                    - grade
                    - xianfanstatus
                  properties:
                    id:
                      type: number
                      example: 0
                    xianfanstatus:
                      type: number
                      example: 0
                  type: object
                self:
                  required:
                    - roleid
                    - rolename
                    - server_id
                    - gender
                    - grade
                    - clazz
                    - titleid
                    - title
                    - gangid
                    - gang
                    - used_name
                    - fightingcapacity
                    - usednamestatus
                    - xianfanstatus
                    - server_name
                    - id
                    - name
                  properties:
                    roleid:
                      type: number
                      example: *********
                    rolename:
                      type: string
                      example: '*********'
                    server_id:
                      type: number
                      example: 1
                    gender:
                      type: number
                      example: 0
                    grade:
                      type: number
                      example: 160
                    clazz:
                      type: number
                      example: 7
                    titleid:
                      type: number
                      example: 0
                    gangid:
                      type: string
                      example: '0'
                    gang:
                      type: string
                      example: ''
                    used_name:
                      type: array
                      items:
                        type: string
                      example:
                        - 幽灵随心
                    fightingcapacity:
                      type: string
                      example: 0,611526;1,95106;2,99257;3,62324;4,11812;5,291200;6,0;7,0;8,51827;9,0;10,0
                    usednamestatus:
                      type: number
                      example: 0
                    xianfanstatus:
                      type: number
                      example: 1
                    server_name:
                      type: string
                      example: ''
                    id:
                      type: number
                      example: *********
                    name:
                      type: string
                      example: '*********'
                  type: object
              type: object
            updatetime:
              type: number
              example: 1616055899766
            expression_base:
              required:
                - expression
                - expression_txt
                - sticker
                - frame
              properties:
                expression:
                  type: number
                  example: 0
                expression_txt:
                  type: number
                  example: 0
                sticker:
                  type: number
                  example: 0
                frame:
                  type: number
                  example: 0
              type: object
            expression_extra:
              required:
                - expression_txt_offset_x
                - expression_txt_offset_y
                - expression_txt_scale_x
                - expression_txt_scale_y
                - expression_txt_rotation
                - sticker_offset_x
                - sticker_offset_y
                - sticker_scale_x
                - sticker_scale_y
                - sticker_rotation
              properties:
                expression_txt_offset_x:
                  type: number
                  example: 0
                expression_txt_offset_y:
                  type: number
                  example: 0
                expression_txt_scale_x:
                  type: number
                  example: 100
                expression_txt_scale_y:
                  type: number
                  example: 100
                expression_txt_rotation:
                  type: number
                  example: 0
                sticker_offset_x:
                  type: number
                  example: 0
                sticker_offset_y:
                  type: number
                  example: 0
                sticker_scale_x:
                  type: number
                  example: 78
                sticker_scale_y:
                  type: number
                  example: 78
                sticker_rotation:
                  type: number
                  example: 0
              type: object
            lastloginip:
              type: string
              example: **************
            lianghao:
              required:
                - id
                - expiredtime
                - hideicon
                - showdigit
              properties:
                id:
                  type: number
                  example: 666666
                expiredtime:
                  type: number
                  example: 1577761481
                hideicon:
                  type: number
                  example: 0
                showdigit:
                  type: number
                  example: 0
              type: object
            rolename:
              type: string
              example: '*********'
            server_id:
              type: number
              example: 1
            gender:
              type: number
              example: 0
            grade:
              type: number
              example: 160
            clazz:
              type: number
              example: 7
            titleid:
              type: number
              example: 0
            title:
              required:
                - id
                - name
              properties:
                id:
                  type: number
                  example: 0
              type: object
            gangid:
              type: string
              example: '0'
            gang:
              type: string
              example: ''
            used_name:
              type: array
              items:
                type: string
              example:
                - 幽灵随心
            fightingcapacity:
              type: string
              example: 0,611526;1,95106;2,99257;3,62324;4,11812;5,291200;6,0;7,0;8,51827;9,0;10,0
            usednamestatus:
              type: number
              example: 0
            xianfanstatus:
              type: number
              example: 1
            server_name:
              type: string
              example: ''
            guild:
              required:
                - id
                - name
              properties:
                id:
                  type: string
                  example: '0'
                name:
                  type: string
                  example: ''
              type: object
            hideusedname:
              type: boolean
              example: false
            isfollowing:
              type: boolean
              example: false
            slientLove:
              $ref: '#/components/schemas/ESlientLoveStatus'
            background:
              required:
                - backgroundid
                - backgroundvalidity
              properties:
                backgroundid:
                  type: number
                  example: 2
                backgroundvalidity:
                  type: number
                  example: 1616061085596122
              type: object
          type: object
    syncBackgroundReq:
      required:
        - roleid
        - id
        - validity
      properties:
        roleid:
          type: number
          example: *********
        id:
          type: number
          description: 背景图片id 为1到9
          example: 1
        validity:
          type: number
          description: 背景有效时间点 时间戳
          example: 1616061085596
    syncBackgroundRes:
      required:
        - code
        - data
      properties:
        code:
          type: number
          example: 0
        data:
          required:
            - affectedRows
          properties:
            affectedRows:
              type: number
              example: 1
          type: object
    PaginationMeta:
      type: object
      description: 分页结构元信息
      properties:
        totalPage:
          type: number
        curPage:
          type: number
        totalCount:
          type: number
    ReportLogItem:
      required:
        - id
        - deviceId
        - url
        - createTime
      properties:
        id:
          type: number
          example: 1
        deviceId:
          description: 设备id
          type: string
          example: device_id
        url:
          type: string
          example: http://device_example.log
        createTime:
          type: number
          example: 1617179813989
    FireworkPhotoListRes:
      type: object
      required:
        - code
        - data
      properties:
        code:
          type: number
          example: 0
        data:
          type: object
          required:
            - list
            - count
          properties:
            list:
              type: array
              items:
                type: object
                required:
                  - id
                  - index
                  - roleId
                  - url
                  - auditStatus
                properties:
                  id:
                    type: number
                    example: 1
                  index:
                    type: number
                    example: 1
                  roleId:
                    type: number
                    example: *********
                  url:
                    type: string
                    example: http://hi-163-qnm.nosdn.127.net/fireworks/202011/09/3ec1a350223911eb9d40d59fffee58e4.jpg
                  auditStatus:
                    type: number
                    example: 0
            count:
              type: number
              example: 2
    NosToken:
      required:
        - token
        - bucketname
        - objectname
        - expires
        - prefix
      properties:
        token:
          type: string
          example: UPLOAD 80daf02d6f7042b190ef3358091cc335:Vywi0Ft15554tdPO35a9ew23PdWJ4c8Fmkx4beZJaWw=:eyJCdWNrZXQiOiJoaS0xNjMtcW5tIiwiT2JqZWN0IjoicGhvdG8vMjAyMTAyLzIzLzBkMzEwYTcwNzU4NTExZWI4NTAwNTcxNTU3NDBkOTIyLmpwZyIsIkV4cGlyZXMiOjE2MTQwNTA1ODcsIlJldHVybkJvZHkiOiJ7XCJjb2RlXCI6MCxcImRhdGFcIjp7XCJidWNrZXRuYW1lXCI6XCIkKEJ1Y2tldClcIixcInVybFwiOlwiaHR0cDovLyQoQnVja2V0KS5ub3Nkbi4xMjcubmV0LyQoT2JqZWN0KVwifX0iLCJDYWxsYmFja1VybCI6IiIsIkNhbGxiYWNrQm9keSI6IiJ9
        bucketname:
          type: string
          example: hi-163-qnm
        objectname:
          type: string
          example: photo/202102/23/0d310a70758511eb850057155740d922.jpg
        expires:
          type: number
          example: 1614050587
        prefix:
          type: string
          example: http://hi-163-qnm.nosdn.127.net/
      type: object
    GetLocationData:
      type: object
      required:
        - country
        - province
        - city
      properties:
        country:
          type: string
          example: 中国
        province:
          type: string
          example: 浙江
        city:
          type: string
          example: 杭州
    FacePinchWebPublicShareInfo:
      properties:
        shareId:
          type: string
          example: uOh7I_nKZmj9w6wHLP5fj
        image:
          type: string
          description: 捏脸站游戏内上传截图(aka 分享页的封面)
          example: https://l10-md-cn.fp.ps.netease.com/file/64dc945202af4626152bf478i9JjHSy205
    FacePinchWebPublicShareVideoInfo:
      properties:
        shareId:
          type: string
          example: uOh7I_nKZmj9w6wHLP5fj
        video:
          type: string
          description: 捏脸视频url
          example: http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4
    AddWishPayload:
      properties:
        roleid:
          type: number
          example: *********
        wishId:
          type: string
          example: 9c3dbc74f33dd3f2
        type:
          type: number
          description: |
            | value | desc         |
            |-------|--------------|
            | 0     | 文本类型心愿 |
            | 1     | 物品类型心愿 |
          example: 1
          enum:
            - 0
            - 1
        text:
          type: string
          example: example text
        templateId:
          type: number
          example: 111
        regionId:
          type: number
          example: 1
        num:
          type: number
          example: 11
        totalProgress:
          type: number
          example: 11
        startTime:
          type: number
          example: 1599725110252
        endTime:
          type: number
          example: 1599725110252
        shareType:
          type: number
          description: |
            | value | desc |
            | --    | --   |
            | 0     | 默认类型分享动态   |
            | 1     | 年终总结分享动态的类型   |
        shareImgs:
          type: array
          description: 心愿分享生成的动态的图片列表
          items:
            type: string
            example: http://hi-163-qnm.nosdn.127.net/moment/201609/01/e18bff00700311e6a36399e1934d111d
    AuditStatus:
      description: |
        | status | description |
        | ---- | -- |
        | 0 | 审核中 |
        | 1 | 审核通过 |
        | -1 | 审核拒绝 |
      type: number
      enum:
        - 0
        - 1
        - -1
    Gender:
      type: integer
      description: |
        | sex | value |
        | ---- | -- |
        | male | 0 |
        | female | 1 |
      enum:
        - 0
        - 1
    Clazz:
      description: |
        | id | name |
        |------|--------|
        | 1  | 射手   |
        | 2  | 甲士   |
        | 3  | 刀客   |
        | 4  | 侠客   |
        | 5  | 方士   |
        | 6  | 医师   |
        | 7  | 魅者   |
        | 8  | 异人   |
        | 9  | 偃师   |
        | 11  | 影灵   |
        | 12  | 蝶客  |
      type: integer
      enum:
        - 1
        - 2
        - 3
        - 4
        - 5
        - 6
        - 7
        - 8
        - 9
        - 11
        - 12
      example: 12
    JobAvatar:
      description: |
        由gender和jobId可生成  ```https://hi-163-qnm.nosdn.127.net/avatars/job_${clazz}_${sex}.jpg```
      example: https://hi-163-qnm.nosdn.127.net/avatars/job_1_male.jpg
      type: string
    Topic:
      type: object
      properties:
        ID:
          type: number
        Name:
          type: string
        Banner:
          type: string
        Url:
          type: string
        Desc:
          type: string
        CreateTime:
          type: number
    Article:
      type: object
      properties:
        title:
          type: string
        content:
          type: string
    ReachFollowLimitError:
      type: object
      properties:
        code:
          type: number
        msg:
          type: string
    SuccRes:
      type: object
      properties:
        code:
          type: number
    ApiAction:
      type: object
      required:
        - isOk
      properties:
        isOk:
          type: boolean
          description: 操作是否正常
          example: true
    ApiOkData:
      type: object
      example: null
    CommonAdd:
      type: object
      required:
        - id
      properties:
        id:
          type: number
          description: 添加生成的id
          example: 10
    TagFilterSchema:
      type: object
      properties:
        selectAllTags:
          type: boolean
          description: 是否选择所有tag, 默认为true
          default: true
        tagIds:
          type: array
          description: 只在selectAllTags=false的时候生效, 标签id列表, 0代表选中其他标签
          items:
            type: number
            example: 1
    MomentAllServerHotListRequestBody:
      allOf:
        - type: object
          properties:
            roleid:
              type: number
              example: *********
            lastid:
              type: number
              description: 指返回里的hot_index, 从1开始计数
              example: 10
            textStyle:
              type: string
              description: 文本风格
              enum:
                - plain
                - html
              default: plain
        - $ref: '#/components/schemas/TagFilterSchema'
    MomentHotListRequestBody:
      allOf:
        - $ref: '#/components/schemas/MomentAllServerHotListRequestBody'
        - type: object
          properties:
            serverid:
              type: number
              example: 1
    MomentListByGuildReq:
      allOf:
        - type: object
          properties:
            roleid:
              type: number
              example: *********
            guildid:
              type: number
              example: *********
            lastid:
              description: 返回比该id小的数据
              type: number
            textStyle:
              type: string
              description: 文本风格
              enum:
                - plain
                - html
              default: plain
        - $ref: '#/components/schemas/TagFilterSchema'
    MomentListRequestBody:
      type: object
      properties:
        roleid:
          type: number
          example: *********
        targetid:
          type: number
          example: *********
        lastid:
          description: 返回比该id小的数据
          type: number
        textStyle:
          type: string
          description: 文本风格
          enum:
            - plain
            - html
          default: plain
        selectAllTags:
          type: boolean
          description: 是否选择所有tag, 默认为true
          default: true
        tagIds:
          type: array
          description: 只在selectAllTags=false的时候生效, 标签id列表, 0代表选中其他标签
          items:
            type: number
            example: 1
    MomentAddTagIds:
      type: array
      description: 标签id列表
      items:
        type: number
        example: 1
    MomentAddRequestBody:
      type: object
      properties:
        roleid:
          type: number
          example: *********
        text:
          type: string
          example: 我是文本
        imglist:
          type: string
          example: '["http://hi-163-qnm.nosdn.127.net/moment/201609/01/67f70920700111e6a36399e1934d111d"]'
        videolist:
          type: string
          example: '["http://hi-163-qnm.nosdn.127.net/upload/201905/13/deaab8a0754e11e9bbd4a9db24757383.mp4"]'
        tagIds:
          $ref: '#/components/schemas/MomentAddTagIds'
    music-club-create-req:
      type: object
      properties:
        musicClubId:
          type: integer
          format: int64
          description: 乐团ID
          example: 1
        name:
          type: string
          description: 乐团名称
          example: 梦幻交响乐团
        serverId:
          type: integer
          format: int64
          description: 服务器ID
          example: 548
        managerRoleId:
          type: integer
          format: int64
          description: 经理角色ID
          example: 281851200548
      required:
        - musicClubId
        - name
        - serverId
        - managerRoleId
    music-club-create-resp:
      type: object
      properties:
        code:
          type: integer
          format: int32
          description: 状态码
          example: 0
        data:
          type: object
          required:
            - musicClubId
            - isOverwrite
          properties:
            musicClubId:
              type: integer
              format: int64
              description: 乐团ID
              example: 1
            isOverwrite:
              type: boolean
              description: 是否覆盖创建, true-覆盖创建, false-正常创建
              example: false
    music-club-disband-resp:
      type: object
      properties:
        code:
          type: integer
          format: int32
          description: 状态码
          example: 0
        data:
          type: object
          required:
            - musicClubId
            - disbandTime
          properties:
            musicClubId:
              type: integer
              format: int64
              description: 乐团ID
              example: 1
            disbandTime:
              type: integer
              format: int64
              description: 解散时间戳
              example: 1718000000000
    music-club-recording-lead-single:
      type: object
      description: 乐团主打唱片
      required:
        - id
        - name
        - hot
      properties:
        id:
          type: number
          description: 唱片id
          example: 1
        name:
          type: string
          description: 唱片名称
          example: 唱片名字
        hot:
          type: number
          description: 唱片热度
          example: 100
    music-club-show-resp:
      type: object
      required:
        - musicClubId
        - name
        - rank
        - recordingNum
        - leadingRecording
      properties:
        musicClubId:
          type: integer
          format: int64
          description: 乐团ID
          example: 1
        name:
          type: string
          description: 乐团名称
          example: 梦幻交响乐团
        rank:
          type: integer
          format: int64
          description: 500名内显示具体名次，超出500名输出0
          example: 1
        recordingNum:
          type: integer
          format: int64
          description: 在架唱片数量
          example: 10
        leadingRecording:
          $ref: '#/components/schemas/music-club-recording-lead-single'
    music-club-update-req:
      type: object
      required:
        - musicClubId
      properties:
        musicClubId:
          type: integer
          format: int64
          description: 乐团ID
          example: 1
        name:
          type: string
          description: 乐团名称
          example: 梦幻交响乐团
        level:
          type: integer
          description: 新等级
          example: 1
        managerRoleId:
          type: integer
          format: int64
          description: 新经理角色ID
          example: 11
    music-club-update-resp:
      type: object
      properties:
        code:
          type: integer
          format: int32
          description: 状态码
          example: 0
        data:
          type: object
          required:
            - musicClubId
            - updateTime
          properties:
            musicClubId:
              type: integer
              format: int64
              description: 乐团ID
              example: 1
            updateTime:
              type: integer
              format: int64
              description: 更新时间戳
              example: 1718000000000
    music-club-rank-item:
      type: object
      required:
        - rank
        - id
        - name
        - grade
        - serverId
        - leadSingleRecording
        - hot
      properties:
        rank:
          type: number
          description: 排名
          example: 1
        id:
          type: number
          description: 乐团ID
        serverId:
          type: number
          description: 服务器ID
          example: 1
        name:
          type: string
          description: 乐团名称
          example: 乐团名字
        grade:
          type: number
          description: 乐团等级
          example: 1
        leadSingleRecording:
          $ref: '#/components/schemas/music-club-recording-lead-single'
        hot:
          type: number
          description: 乐团热度
          example: 100
    music-club-recording-release-req:
      type: object
      description: 唱片上架请求所需信息
      required:
        - trackId
        - name
        - chorusStart
        - vocalOffset
        - vocalVolume
        - instrumentVolume
        - musicClubId
        - serverId
        - dataUrl
        - releaseTime
        - vocalUrl
        - duration
      properties:
        trackId:
          type: string
          description: 唱片录制唯一id, 乐队演出后在本地录制后在本地就能生成, 游戏方生成, 用于唯一标识一个唱片录制
          maxLength: 16
          example: 1234567890abcdef
        name:
          type: string
          description: 唱片名称
          example: 唱片名字
        chorusStart:
          type: integer
          description: 副歌开始位置，单位秒
          minimum: 0
          example: 10
        vocalOffset:
          type: integer
          description: 人声偏移时间，单位毫秒, 正数表示人声提前，负数表示人声延后
          example: 10
        vocalVolume:
          type: number
          description: 人声音量比列
          minimum: 0
          maximum: 100
          example: 70
        serverId:
          type: integer
          description: 服务器id
          example: 1
        instrumentVolume:
          type: number
          description: 乐器声音量比列
          minimum: 0
          maximum: 100
          example: 70
        dataUrl:
          type: string
          description: 唱片数据url
          example: http://fp.netease.com/music_club/recording/my_recording.data
        musicClubId:
          type: integer
          description: 唱片所属乐团id
          example: 1
        vocalUrl:
          type: string
          description: 人声音频文件URL，用于人声审核
          minLength: 1
          example: http://fp.netease.com/music_club/recording/my_vocal.mp3
        duration:
          type: integer
          description: 唱片时长，单位毫秒
          minimum: 0
          example: 180000
    music-club-recording-release-resp:
      type: object
      description: 唱片上架返回信息
      required:
        - id
        - releaseTime
      properties:
        id:
          type: integer
          description: 唱片发行id, 只有唱片上架后才有
          example: 1
        releaseTime:
          type: integer
          description: 发行时间, 是指唱片上架的时间, 单位ms
          example: 1716835200000
    music-club-rank-tiny:
      type: object
      required:
        - id
        - serverId
        - name
      properties:
        id:
          type: number
          description: 乐团ID
        serverId:
          type: number
          description: 服务器ID
          example: 1
        name:
          type: string
          description: 乐团名称
          example: 乐团名字
    music-club-recording-show:
      type: object
      description: 点播界面的乐团唱片展示信息
      allOf:
        - $ref: '#/components/schemas/music-club-recording-release-req'
        - $ref: '#/components/schemas/music-club-recording-release-resp'
        - type: object
          required:
            - musicClub
            - requestPlayCount
            - rating
            - myRating
            - isRated
          properties:
            musicClub:
              $ref: '#/components/schemas/music-club-rank-tiny'
            requestPlayCount:
              type: integer
              description: 点播次数
              example: 100
            rating:
              type: number
              description: 评分
              example: 9.8
            myRating:
              type: number
              description: 我的评分
              example: 4.5
            isRated:
              type: boolean
              description: 当前用户是否已评分
              example: true
    music-club-radio-request-play-req:
      type: object
      description: 同步点播操作行为请求所需信息
      required:
        - recordingId
        - fromRoleId
        - toRoleId
        - eventTime
      properties:
        fromRoleId:
          type: integer
          description: 发起点播角色ID
          example: 1
        toRoleId:
          type: integer
          description: 接收点播角色ID
          example: 1
        recordingId:
          type: integer
          description: 唱片发行id
          example: 1
        eventTime:
          type: integer
          description: 事件时间戳, 单位ms
          example: 1716888888000
    music-club-radio-request-play-resp:
      type: object
      description: 同步点播操作行为请求所需信息
      required:
        - recordingId
      properties:
        recordingId:
          type: integer
          description: 唱片发行id
          example: 1
    music-club-recording-audit-callback-req:
      type: object
      required:
        - vocalUrl
        - auditStatus
      properties:
        vocalUrl:
          type: string
          description: 人声音频文件URL
          minLength: 1
          example: https://example.com/vocal/abc123.mp3
        auditStatus:
          type: integer
          description: 审核状态，1表示通过，-1表示拒绝
          enum:
            - 1
            - -1
          example: 1
        rejectReason:
          type: string
          description: 审核拒绝原因，当auditStatus为-1时使用
          example: 包含不当内容
    music-club-recording-audit-callback-resp:
      type: object
      properties:
        code:
          type: integer
          format: int32
          description: 状态码
          example: 0
        data:
          type: object
          properties:
            success:
              type: boolean
              description: 处理是否成功
              example: true
    music-club-recording-remove-resp:
      type: object
      description: 唱片下架返回信息
      required:
        - id
        - removeTime
      properties:
        id:
          type: integer
          description: 唱片发行id, 只有唱片上架后才有
          example: 1
        removeTime:
          type: integer
          description: 指唱片下架的时间, 单位ms
          example: 1716835200000
    music-club-recording-rate-req:
      type: object
      description: 唱片打分请求所需信息
      required:
        - rating
        - recordingId
      properties:
        rating:
          type: number
          minimum: 1
          maximum: 10
          description: 打分 1-10分
          example: 1
        recordingId:
          type: integer
          description: 唱片发行id
          example: 1
    music-club-recording-rate-resp:
      type: object
      description: 唱片打分返回信息
      required:
        - id
        - ratingTime
      properties:
        id:
          type: integer
          description: 打分记录id
          example: 1
        ratingTime:
          type: integer
          description: 打分时间, 单位ms
          example: 1716835200000
    moment-list-item-show-lottery:
      type: object
      description: 玩家和当前抽奖动态的相关关系
      required:
        - drawStatus
        - isParticipate
        - isMyLottery
        - isWin
      properties:
        drawStatus:
          type: integer
          description: 抽奖状态 0 未开奖 1 已开奖 2 已取消
          example: 1
        isParticipate:
          type: boolean
          description: 参与状态 0 未参与 1 已参与
          example: 是否参与
        isMyLottery:
          type: boolean
          description: 是否是自己发起的抽奖
          example: true
        isWin:
          type: boolean
          description: 当前玩家是否已经中奖
          example: true
    auth-login-resp:
      type: object
      description: 角色登录梦岛返回数据
      properties:
        skey:
          type: string
          example: WyIxNzQ5NzIzMTU1MDAwIiwicm9sZSIsIjUyMDIwMjQ4NSIsIlFOTW9iaWxlMTUyNzExNzE1Il0.0eHIaf96QhqSC5G_Vm3o4DL0TDeUhVXNkFJRdNWMgZ8
          description: 会话密钥(Session Key)
        roleid:
          type: integer
          example: 520202485
          description: 角色ID
        renqi:
          type: integer
          example: 0
          description: 人气值
        flower:
          type: integer
          example: 0
          description: 鲜花数量
        flowerrenqi:
          type: integer
          example: 0
          description: 鲜花人气值
        sendflowerrenqi:
          type: integer
          example: 0
          description: 赠送鲜花人气值
        gift:
          type: integer
          example: 0
          description: 礼物数量
        photo:
          type: string
          nullable: true
          example: null
          description: 用户照片URL(可为空)
        hasphoto:
          type: integer
          example: 0
          description: 是否有照片(0-无 1-有)
        renqi_weekly:
          type: integer
          example: 0
          description: 本周人气值
        flower_weekly:
          type: integer
          example: 0
          description: 本周鲜花数量
        flowerrenqi_weekly:
          type: integer
          example: 0
          description: 本周鲜花人气值
        sendflowerrenqi_weekly:
          type: integer
          example: 0
          description: 本周赠送鲜花人气值
        gift_weekly:
          type: integer
          example: 0
          description: 本周礼物数量
    inform:
      type: object
      properties:
        id:
          type: integer
          example: 43970
        roleid:
          type: integer
          example: *********
        targetid:
          type: integer
          example: *********
        objectid:
          type: integer
          example: 154705111
        relateid:
          type: string
          nullable: true
          example: ''
        text:
          type: string
          nullable: true
          example: 开奖了，看看是谁中奖了?
        type:
          type: integer
          description: |
            | type  | en                     | cn               |
            |-------|-----------------------|------------------|
            | 0     | Like                   | 点赞             |
            | 1     | Comment                | 评论             |
            | 2     | Replay                 | 回复             |
            | 3     | Forward                | 转发             |
            | 4     | AtInMoment             | 在动态中@提及    |
            | 5     | AtInComment            | 在评论中@提及    |
            | 6     | MomentLotteryDraw      | 动态抽奖开奖     |
            | 7     | MomentLotteryCancel    | 动态抽奖取消开奖 |
          example: 6
        status:
          type: integer
          example: 1
        createtime:
          type: integer
          example: 1739453138319
        photo:
          type: string
          nullable: true
          example: https://hi-163-qnm.nosdn.127.net/photo/202412/25/f96ed6b0c28e11ef8a090b4b50ca6217?watermark&type=1&gravity=center&image=Y29tbW9uL3dhdGVybWFyay1hdWRpdC5wbmc=
        showphoto:
          type: integer
          example: 1
        photoaudit:
          type: integer
          example: 0
        friendlist:
          type: string
          example: 100200001,100300001,100400001,101100001,101200001
        privacy:
          type: string
          nullable: true
        expression_base:
          type: string
          example: '{"expression":0,"expression_txt":0,"sticker":0,"frame":47,"portraitid":7}'
        splevel:
          type: integer
          example: 0
        lianghao:
          type: object
          properties:
            id:
              type: integer
              example: 0
            expiredtime:
              type: integer
              example: 0
            hideicon:
              type: integer
              example: 0
            showdigit:
              type: integer
              example: 0
        rolename:
          type: string
          example: 星樱桃
        gender:
          type: integer
          example: 1
        grade:
          type: integer
          example: 150
        clazz:
          type: integer
          example: 8
        server_id:
          type: integer
          example: 1
        used_name:
          type: string
          example: ''
        xianfanstatus:
          type: integer
          example: 0
        server_name:
          type: string
          example: ''
        servername:
          type: string
          example: ''
        objectinfo:
          type: object
          properties:
            id:
              type: integer
              example: 154705111
            roleid:
              type: integer
              example: *********
            text:
              type: string
              example: 我是文本
            imglist:
              type: array
              items:
                type: object
                properties:
                  pic:
                    type: string
                    example: https://hi-163-qnm.nosdn.127.net/moment/201609/01/67f70920700111e6a36399e1934d111d
                  thumb:
                    type: string
                    example: https://hi-163-qnm.nosdn.127.net/moment/201609/01/67f70920700111e6a36399e1934d111d?imageView&thumbnail=250y160
            imgaudit:
              type: string
              nullable: true
              example: '1'
            videolist:
              type: string
              nullable: true
              example: ''
    moment-lottery-requirements:
      type: object
      description: 中奖参与资格要求, 至少选择一个
      properties:
        like:
          type: boolean
          description: 参与抽奖需要点赞
        comment:
          type: boolean
          description: 参与抽奖需要评论
        forward:
          type: boolean
          description: 参与抽奖需要转发
        follow:
          type: boolean
          description: 参与抽奖需要关注
      required:
        - like
        - comment
        - forward
        - follow
    moment-lottery-show:
      allOf:
        - $ref: '#/components/schemas/moment-list-item-show-lottery'
        - type: object
          required:
            - type
            - participateNum
            - winPrizes
            - drawTime
            - requirements
            - winnerNum
            - minLevel
            - myAction
            - serverScope
            - hostPlayer
          properties:
            momentId:
              type: number
              example: 15938804987
            type:
              type: integer
              description: 抽奖类型 1:阳光普照 2:天选之人
              example: 1
            winnerNum:
              type: integer
              description: 中奖人数
              example: 100
            minLevel:
              type: integer
              description: 最低参与等级
              example: 0
            participateNum:
              type: number
              description: 当前参与人数
              example: 999
            winPrizes:
              description: 中奖后可获得的礼品
              type: array
              items:
                type: object
                required:
                  - id
                  - num
                properties:
                  id:
                    type: integer
                    description: 奖品id
                    example: 21000039
                  num:
                    type: integer
                    description: 奖品的数量
                    example: 5
            winnerRoleName:
              type: string
              description: 中奖玩家的角色名, 多个中奖者的时候显示中奖者中参与最早的
              example: 玩家名字七个字
            drawTime:
              type: integer
              description: 开奖时间(ms)
            requirements:
              $ref: '#/components/schemas/moment-lottery-requirements'
            serverScope:
              type: string
              description: 参与抽奖范围, 全服或者本服
              enum:
                - all
                - local
            hostPlayer:
              type: object
              description: 发起者信息
              properties:
                roleId:
                  type: number
                  description: 发起者角色id
                serverId:
                  type: number
                  description: 服务器id
                roleName:
                  type: string
                  description: 发起者角色名
                serverName:
                  type: string
                  description: 服务器名
            myAction:
              type: object
              description: 计算抽奖要求中我当前满足的操作
              properties:
                isLike:
                  type: boolean
                  description: 是否已经点赞过
                isComment:
                  type: boolean
                  description: 是否已经评论过
                isForward:
                  type: boolean
                  description: 是否已经转发过
                isFollow:
                  type: boolean
                  description: 是否已经关注过
  parameters:
    atPlayersKw:
      name: kw
      in: query
      description: 搜索关键词
      schema:
        type: string
        maxLength: 11
        minLength: 1
        example: 搜索角色名或者角色id
    cardIds:
      required: true
      name: card_ids
      in: query
      description: 关联卡片id列表(CSV格式)
      schema:
        type: string
        example: 2754,2753
    level:
      required: true
      name: level
      in: query
      description: 角色等级
      schema:
        type: integer
        example: 20
    cardId:
      required: true
      name: card_id
      in: query
      description: 卡片id
      schema:
        type: integer
        example: 2754
    commentId:
      required: true
      name: comment_id
      in: query
      description: 评论id
      schema:
        type: number
        example: 10
    cardNotionId:
      required: true
      name: notionId
      in: query
      description: 卡片想法id
      schema:
        type: number
        example: 10
    cardNotionCommentId:
      required: true
      name: commentId
      in: query
      description: 卡片评论id
      schema:
        type: number
        example: 10
    cardNotionText:
      required: true
      name: text
      in: query
      description: 卡片想法评论文本
      schema:
        type: string
        example: 这段剧情这的特别棒
    replyCommentId:
      name: replyCommentId
      in: query
      description: 回复的评论id
      schema:
        type: number
        example: 12
    complainTargetUser:
      required: true
      name: targetid
      in: query
      description: 被举报玩家角色id
      schema:
        type: number
        example: 100100002
    complainResouceType:
      required: true
      name: resouce_type
      in: query
      description: 举报资源类型 card_notion => 剧情卡想法;  card_notion_comment => 剧情卡评论
      schema:
        type: string
        example: card_notion
    complainResouceId:
      required: true
      name: resouce_id
      in: query
      description: 举报资源类型是card_notion的时候就是想法id, 评论的时候就是评论id, 方便定位举报资源
      schema:
        type: number
        example: 1
    complainContent:
      required: true
      name: content
      in: query
      description: 举报文本
      schema:
        type: string
        example: xxx， 你是个大SB
    complainReason:
      required: true
      name: reason
      in: query
      description: 举报理由
      schema:
        type: string
        example: 多次辱骂玩家
    activityName:
      name: activity_bame
      in: path
      required: true
      description: 活动名字
      schema:
        type: string
        example: qyfh2023
    fashionLotteryItemLevel:
      required: false
      name: itemLevel
      in: query
      description: 时装道具等级 精致， 豪华，至尊
      schema:
        type: number
        example: 10
    cloudGameAccount:
      required: true
      name: account
      in: query
      description: 账号
      schema:
        type: string
        example: '**********'
    cloudGameRoleId:
      required: true
      name: roleid
      in: query
      description: 角色id, 不存在可传0
      schema:
        type: number
        example: '0'
    cloudGameTime:
      required: true
      name: time
      in: query
      description: 时间戳, 单位s
      schema:
        type: number
        example: **********
    cloudGameAuthToken:
      required: true
      name: token
      in: query
      description: |
        校验token token计算方式为 md5(time + account + roleid + FACE_PINCH_CLOUD_AUTH_SALT)
      schema:
        type: string
        example: 086779eda50242b2af78d79e5991ca78
    dashenAuthToken:
      required: true
      name: token
      in: query
      description: |
        校验token token计算方式为 md5(time + account + roleid + FACE_PINCH_DASHEN_AUTH_SALT)
      schema:
        type: string
        example: 086779eda50242b2af78d79e5991ca78
    jobId:
      name: jobId
      in: query
      description: 职业id
      required: false
      schema:
        type: number
        description: 作品所能应用职业id
        example: '0'
    gender:
      name: gender
      in: query
      description: 性别
      required: false
      schema:
        type: number
        description: 作品所能应用性别
        example: '0'
    qmpkDate:
      required: false
      name: ds
      in: query
      description: 查询的日期 (默认查前一日榜单) 格式 yyyy-MM-dd
      schema:
        type: string
        example: '2022-06-30'
    notificationId:
      required: true
      name: id
      in: query
      description: 通知id
      schema:
        type: number
        example: 13
    readType:
      required: false
      name: type
      in: query
      description: 通知状态 0 => 未读 1 => 已读
      schema:
        type: number
        enum:
          - 0
          - 1
        example: 0
    kw:
      required: false
      name: kw
      in: query
      description: 搜索关键字
      schema:
        type: string
        example: 抽奖
    roleId:
      required: true
      name: roleId
      in: query
      description: 角色id
      schema:
        type: number
        example: *********
    chat_photo_id:
      required: true
      name: photo_id
      in: query
      description: 图片id
      schema:
        type: number
        example: 10
    chat_photo_ids:
      required: true
      name: photo_ids
      in: query
      description: 图片id(csv格式)
      schema:
        type: string
    chat_url:
      required: true
      name: url
      in: query
      description: 图片url
      schema:
        type: string
        example: http://hi-163-qnm.nosdn.127.net/pyqchat/202105/13/04aae220b3c111eb99ec0f95e854b06d
    chat_auth_token:
      required: true
      name: auth_token
      in: query
      description: 图片授权token
      schema:
        type: string
        example: a secure token for auth
    chat_nos_type:
      required: true
      name: type
      in: query
      description: nos图片类型
      schema:
        type: string
        example: pyqchat
    activityLoginMode:
      name: __login_mode
      in: query
      description: |
        #### 登录模式
        -------
          注意变量名前的双下划线, 防止和游戏内自由携带的参数名字冲突可能性

        -  默认是 *legacy* 模式，兼容现有实现(不指定该参数即为该模式), 跳转的地址配置在302的 location 字段中, ios内置浏览器使用该模式有概率会出现 cookie 丢失的问题
        -  新的 *jsbridge* 模式，不在使用302跳转，前端使用 jsbridge 和 unisdk 通信使用原生代码来维持 cookie 状态, 此时无需使用302配置 location 跳转， 携带 location 返回 json 格式即可, 由前端自由控制路由
      required: false
      schema:
        type: string
        enum:
          - legacy
          - jsbridge
        example: jsbridge
    skey:
      name: skey
      in: query
      description: skey
      schema:
        type: string
        example: CHEAT_SKEY_ONLY_FOR_TEST
    facePinchKw:
      name: kw
      in: query
      description: 搜索关键词
      schema:
        type: string
        maxLength: 7
        minLength: 2
        example: 国风
    facePinchSortBy:
      name: sortBy
      in: query
      description: 捏脸站排序方式
      schema:
        type: string
        enum:
          - hot
          - weekHot
          - new
        example: hot
    dateStr:
      name: date
      in: query
      required: false
      description: 日期
      schema:
        type: string
        example: 2023-08-08 12:00:00 +0800
    facePinchShareId:
      name: shareId
      in: query
      description: 捏脸站分享id
      schema:
        type: string
        example: '-Ma2H0aYKTAQpNSdjVBVb'
    facePinchShareVideoId:
      name: shareId
      in: query
      description: 捏脸分享的视频id
      schema:
        type: string
        example: '-Ma2H0aYKTAQpNSdjVBVb'
    facePinchWorkId:
      name: id
      in: query
      description: 捏脸站作品id
      schema:
        type: number
        example: 3
    facePinchShareVideo:
      name: video
      in: query
      description: 捏脸站分享视频url
      schema:
        type: string
        example: http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4
    facePinchVisibility:
      name: visibility
      in: query
      description: 公开级别， 0 => 公开 1 => 私有
      schema:
        type: number
        enum:
          - 0
          - 1
        example: 0
    facePinchFilterParts:
      name: allowedParts
      in: query
      description: |
        筛选组合及对应数值如下:
        | 组合               | 十进制 | 二进制 |
        |-------------------|--------|--------|
        | 仅塑形            | 1      | 001    |
        | 仅妆容            | 2      | 010    |
        | 塑形+妆容         | 3      | 011    |
        | 仅全脸            | 4      | 100    |
        | 塑形+全脸         | 5      | 101    |
        | 妆容+全脸         | 6      | 110    |
        | 塑形+妆容+全脸     | 7      | 111    |
      schema:
        type: number
        minimum: 1
        maximum: 7
        example: 3
    index:
      name: index
      in: query
      description: index
      schema:
        type: integer
        minimum: 0
        maximum: 9
        example: 1
    serverid:
      required: true
      name: serverid
      in: query
      description: 服务器id
      schema:
        type: number
        example: 1
    targetid:
      name: targetid
      in: query
      description: 目标玩家id
      schema:
        type: number
        example: 100100002
    cacelTargetIds:
      name: targetids
      in: query
      description: 目标玩家ids(csv)
      schema:
        type: string
        example: *********,71000001
    targetids:
      name: targetids
      in: query
      description: 目标玩家id数组
      schema:
        type: string
        example: '["*********"]'
    replyid:
      name: replyid
      in: query
      description: 回复的玩家id
      schema:
        type: number
        example: *********
    momentid:
      name: id
      required: true
      in: query
      description: 心情id
      schema:
        type: number
        example: 15938804987
    momentId:
      name: momentId
      required: true
      in: query
      description: 心情id
      schema:
        type: number
        example: 15938804987
    commentid:
      name: id
      required: true
      in: query
      description: 获取比评论id小的评论列表
      schema:
        type: number
        example: 33640
    commentid2:
      name: comment_id
      required: true
      in: query
      description: 评论id
      schema:
        type: number
        example: 33640
    guildid:
      name: guildid
      required: true
      in: query
      description: 帮会id
      schema:
        type: number
        example: 6800002
    text:
      name: text
      in: query
      description: 文本
      schema:
        type: string
        example: example text
    textRequired:
      name: text
      in: query
      description: 文本
      required: true
      schema:
        type: string
        example: example text
    tsOpt:
      name: ts
      in: query
      description: 时间戳(ms) 非必选，不传的时候使用当前时间戳，兼容现有实现
      schema:
        type: number
        example: 1688525977695
    likeAction:
      name: action
      in: query
      schema:
        type: string
        enum:
          - do
          - undo
        default: do
    lastid:
      name: lastid
      in: query
      description: 返回比该id小的数据
      schema:
        type: number
    hotIndex:
      name: lastid
      in: query
      description: 指返回里的hot_index, 从1开始计数
      schema:
        type: number
        default: 1
    page:
      name: page
      in: query
      description: 页码
      schema:
        type: number
        default: 1
        example: 1
    sortBy:
      name: sort_by
      in: query
      schema:
        type: string
        enum:
          - new
          - hot
        example: new
    pageSize:
      name: pageSize
      in: query
      description: 每页大小
      schema:
        type: number
        default: 10
        maximum: 20
        example: 10
    pagesize:
      name: pagesize
      in: query
      description: 每页大小
      schema:
        type: number
        maximum: 20
        default: 10
    page_size:
      name: page_size
      in: query
      description: 每页大小
      schema:
        type: integer
        maximum: 20
        minimum: 1
        example: 10
    page_size_style:
      name: page_size
      in: query
      description: 每页大小
      schema:
        type: number
        default: 10
    nosType:
      name: type
      required: true
      in: query
      schema:
        type: string
        enum:
          - photo
          - moment
          - avatar
          - video
          - tape
          - screenshot
          - fireworks
          - report_log
    nosStyle:
      name: style
      required: true
      in: query
      description: 返回token格式，适配前端组件格式选择web
      schema:
        type: string
        enum:
          - default
          - web
        example: default
    nosExtName:
      name: extname
      required: true
      in: query
      schema:
        type: string
        enum:
          - jpg
          - jpeg
          - png
          - log
    textStyle:
      name: textStyle
      required: true
      in: query
      schema:
        type: string
        enum:
          - plain
          - html
        default: plain
    accountStatus:
      name: status
      in: query
      description: |
        | 枚举值  | 含义  |
        |---|---|
        | 0  | 标记为正常  |
        | -1  | 标记为删除  |
      schema:
        type: number
        enum:
          - 0
          - -1
        example: -1
    m_title:
      name: title
      in: query
      schema:
        type: string
        maxLength: 20
        example: artile title
    m_content:
      name: content
      in: query
      schema:
        type: string
        maxLength: 800
        example: artile content line text
    imglist:
      name: imglist
      in: query
      schema:
        type: string
        example: '["http://hi-163-qnm.nosdn.127.net/moment/201609/01/67f70920700111e6a36399e1934d111d"]'
    videolist:
      name: videolist
      in: query
      schema:
        type: string
        example: '["http://hi-163-qnm.nosdn.127.net/upload/201905/13/deaab8a0754e11e9bbd4a9db24757383.mp4"]'
    eventType:
      name: type
      in: query
      description: |
        | type | meaning |
        | ------------- |:-------------:|
        | 1 | 踩空间 |
        | 2       | 送礼 |
        | 3 | 送鲜花 |
      schema:
        type: number
        enum:
          - 1
          - 2
          - 3
        example: 3
    parameter:
      name: parameter
      in: query
      schema:
        type: string
        example: 21020795$周年庆•以爱之名$1$200
    server_id:
      name: server_id
      in: query
      required: true
      schema:
        type: number
        example: 200
    date_yyyymmdd:
      name: date
      in: query
      schema:
        type: string
        example: '20211124'
    equip_period:
      name: period
      in: query
      schema:
        type: number
        minimum: 1
        maximum: 7
        example: 1
    equip_position:
      name: equip_position
      in: query
      required: true
      schema:
        type: number
        example: 1
    equipId:
      name: equipId
      description: 装备id
      in: query
      required: true
      schema:
        type: string
        example: BBAFCLMHLVktAAAA
    eventtoken:
      name: eventtoken
      in: query
      schema:
        type: string
        example: 2416844468fd5988823ba6993c37eb7a
    time:
      name: time
      in: query
      required: true
      schema:
        type: number
        example: 1599725110
    wishId:
      name: wishId
      in: query
      required: true
      description: 心愿id
      schema:
        maxLength: 16
        type: string
        example: 9c3dbc74f33dd3f2
    deviceId:
      name: device_id
      in: query
      required: true
      description: 设备标志id
      schema:
        type: string
        example: device_id_exampl
    helpText:
      name: text
      in: query
      required: true
      description: 助力文本
      schema:
        maxLength: 16
        type: string
        example: dream come true
    helpId:
      name: helpId
      in: query
      required: true
      description: 助力ID
      schema:
        type: number
        example: 1
    nosUrl:
      name: url
      in: query
      required: true
      description: url
      schema:
        type: string
        example: http://hi-163-qnm.nosdn.127.net/fireworks/202011/09/3ec1a350223911eb9d40d59fffee58e4.jpg
    templateId:
      name: templateId
      in: query
      description: 道具id
      schema:
        type: number
        example: 111
    num:
      name: num
      in: query
      description: 商品数量
      schema:
        type: number
        example: 11
    totalProgress:
      name: totalProgress
      in: query
      description: 总体进度
      schema:
        type: number
        example: 11
    startTime:
      name: startTime
      in: query
      description: 开始时间
      schema:
        type: number
        example: 1599725110252
    endTime:
      name: endTime
      in: query
      description: 结束时间
      schema:
        type: number
        example: 1599725110252
    nonce:
      name: nonce
      in: query
      description: 随机字符串(6位)
      schema:
        minLength: 6
        maxLength: 6
        type: string
        example: awsdef
    ds:
      name: ds
      in: query
      description: 查询登录的日期 (yyyy-MM-dd)
      schema:
        type: string
        example: '2024-08-18'
    ts:
      name: ts
      in: query
      description: 当前时间戳(ms)
      schema:
        type: number
        example: 1723639341038
    process:
      name: process
      in: query
      description: 助力进度
      schema:
        type: number
        example: 11.11
    wishType:
      name: type
      in: query
      description: 心愿单类型  0纯文本  1道具
      schema:
        type: number
        enum:
          - 0
          - 1
    wishStatus:
      name: status
      in: query
      description: 心愿单状态 0 默认，玩家可以助力、删除等操作; 1已领奖; 2, 已删除;  3, 已返还;
      schema:
        type: number
        enum:
          - 0
          - 1
          - 2
          - 3
    roleid:
      name: roleid
      required: true
      in: query
      description: 角色id
      schema:
        type: integer
        example: *********
    timestamp:
      name: timestamp
      in: query
      description: 时间戳(ms)
      required: true
      schema:
        type: number
        example: 1748955456606
    music-club-list-sort:
      name: sortBy
      in: query
      description: |
        | 排序选项 | 说明 |
        |---------|------|
        | requestPlayCount | 点播量降序 |
        | rating | 评分降序 |
        | recentHot | 新晋热门降序, 周一零点开始计算, 用本周点播次数降序 |
      schema:
        type: string
        enum:
          - requestPlayCount
          - rating
          - recentHot
        default: requestPlayCount
        example: requestPlayCount
    music-club-id-filter:
      name: musicClubId
      in: query
      description: 乐团id, 用于实现我的乐团唱片功能，游戏需要传入我的乐团id来过滤出我的乐团唱片
      schema:
        type: integer
        example: 1
    commonPage:
      name: page
      in: query
      description: 页码
      schema:
        type: number
        default: 1
        example: 1
        minimum: 1
    commonPageSize:
      name: pageSize
      in: query
      description: 每页大小
      schema:
        type: number
        default: 10
        maximum: 20
        example: 10
        minimum: 1
    music-club-id:
      name: musicClubId
      in: query
      description: 乐团id
      schema:
        type: integer
        example: 1
    music-club-recording-id:
      name: recordingId
      in: query
      description: 唱片上架后生成的发行id
      schema:
        type: integer
        example: 1
    server:
      name: server
      in: query
      description: 服务器id
      required: true
      schema:
        type: integer
        example: 2480
    authTime:
      name: time
      in: query
      description: 请求时间戳,单位毫秒
      required: true
      schema:
        type: number
        example: *************
    authAccount:
      name: account
      in: query
      description: 用户唯一账号
      required: true
      schema:
        type: string
        example: QNMobile167641579
    authUrs:
      name: urs
      in: query
      required: true
      schema:
        type: string
        description: 网易通行证账号
        example: <EMAIL>
    authAccountId:
      name: accountId
      description: 账号id,包含渠道信息
      in: query
      required: true
      schema:
        type: string
        example: aebgjlqlrj4vvxr7@ios.app_store.win.163.com
    roleLevel:
      name: level
      in: query
      required: true
      description: 角色等级
      schema:
        type: integer
        example: 109
    language:
      name: language
      in: query
      description: 取值为 "cn", "vn", "en", "th", "ina"（中文/越南文/英文/泰文/印尼语）
      schema:
        type: string
        enum:
          - cn
          - vn
          - en
          - th
          - ina
        example: cn
    country:
      name: country
      in: query
      description: 国家代码或地区标识
      schema:
        type: number
        example: 11
    authToken:
      name: token
      in: query
      required: true
      description: token计算方式为 md5(time + account + urs + roleId + AUTH_TOKEN_SALT)
      schema:
        type: string
        example: faccab8a68a80dfc179e1498344f9218
    authRoleId:
      name: roleid
      required: true
      in: query
      description: 角色id
      schema:
        type: number
        example: *********
    lastid2:
      name: lastid
      required: true
      in: query
      description: 传参数一般接口列表最后一项的id, 用于下拉滑动获取更多, 倒叙排序下会返回比该id小的数据, 第一次传0即可
      schema:
        type: number
        example: 43970
        minimum: 0
    inform-pagesize:
      name: pagesize
      in: query
      description: 每页大小
      schema:
        type: number
        default: 30
        maximum: 50
        example: 30
  examples:
    fireworksList:
      value:
        code: 0
        data:
          list:
            - id: 1
              roleId: *********
              index: 1
              url: http://hi-163-qnm.nosdn.127.net/fireworks/202011/09/3ec1a350223911eb9d40d59fffee58e4.jpg
              auditStatus: 0
            - id: 2
              roleId: *********
              index: 2
              url: http://hi-163-qnm.nosdn.127.net/fireworks/202011/09/3ec1a350223911eb9d40d59fffee58e5.jpg
              auditStatus: 1
          count: 2
      summary: A fireworks list
  securitySchemes:
    skeyAuth:
      type: apiKey
      in: query
      name: skey
      description: skey
  responses:
    MomentGetDetailRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/MomentListItemShow'
    MomentGetMomentsRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                type: object
                properties:
                  list:
                    type: array
                    items:
                      $ref: '#/components/schemas/MomentListItemShow'
    DailyLoginLoginTimeRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/DailyLoginLoginTime'
    MomentLotteryWinnersRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/MomentLotteryWinnersData'
    ServerMomentLotteryAddRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/ServerMomentLotteryAddResp'
    ServerTransferAddRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/ServerTransferAddData'
    ThisDayThatYearHasMomentRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/ThisDayThatYearHasMomentData'
    ThisDayThatYearHasMomentListRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/ThisDayThatYearMomentListData'
    MomentListHotRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/MomentListHotData'
    CommonLikeActionRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/CommonLikeAction'
    CardNotionLikeActionRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/CardNotionLikeAction'
    FacePinchWorkAddRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: integer
                description: 返回值,0正常, 其他皆为异常
                example: 0
              data:
                $ref: '#/components/schemas/FacePinchWorkAddRet'
    FacePinchShareVideoAddRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: integer
                description: 返回值,0正常, 其他皆为异常
                example: 0
              data:
                $ref: '#/components/schemas/FacePinchShareVideoAdd'
    FacePinchWorkListSelfRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: integer
                description: 返回值,0正常, 其他皆为异常
                example: 0
              data:
                type: object
                required:
                  - list
                  - usage
                properties:
                  list:
                    type: array
                    items:
                      $ref: '#/components/schemas/FacePinchWorkItem'
                  usage:
                    $ref: '#/components/schemas/FacePinchWorkUploadUsage'
    FacePinchCloudGameAuthLoginRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: integer
                description: 返回值,0正常, 其他皆为异常
                example: 0
              data:
                type: object
                required:
                  - skey
                properties:
                  skey:
                    type: string
                    example: CHEAT_SKEY_ONLY_FOR_TEST
    MomentTagsRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: integer
                description: 返回值,0正常, 其他皆为异常
                example: 0
              data:
                $ref: '#/components/schemas/MomentTagList'
    AtPlayersListRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: integer
                description: 返回值,0正常, 其他皆为异常
                example: 0
              data:
                $ref: '#/components/schemas/AtPlayersList'
    FashionLotteryListRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: integer
                description: 返回值,0正常, 其他皆为异常
                example: 0
              data:
                $ref: '#/components/schemas/FashionLotteryList'
    FacePinchWebPublicShareInfoRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: integer
                description: 返回值,0正常, 其他皆为异常
                example: 0
              data:
                $ref: '#/components/schemas/FacePinchWebPublicShareInfo'
    FacePinchWebPublicShareVideoInfoRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: integer
                description: 返回值,0正常, 其他皆为异常
                example: 0
              data:
                $ref: '#/components/schemas/FacePinchWebPublicShareVideoInfo'
    FacePinchWorkListRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: integer
                description: 返回值,0正常, 其他皆为异常
                example: 0
              data:
                type: object
                required:
                  - list
                properties:
                  list:
                    type: array
                    items:
                      $ref: '#/components/schemas/FacePinchWorkItem'
    FacePinchWorkListCollectRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: integer
                description: 返回值,0正常, 其他皆为异常
                example: 0
              data:
                type: object
                properties:
                  list:
                    type: array
                    items:
                      $ref: '#/components/schemas/FacePinchWorkItem'
                  usage:
                    $ref: '#/components/schemas/FacePinchWorkCollectUsage'
    FacePinchGetFpTokenRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: integer
                description: 返回值,0正常, 其他皆为异常
                example: 0
              data:
                $ref: '#/components/schemas/FacePinchGetFpToken'
    FacePinchWorkDetailRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: integer
                description: 返回值,0正常, 其他皆为异常
                example: 0
              data:
                $ref: '#/components/schemas/FacePinchWorkItem'
    FacePinchWorkUpdateVisibilityRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: integer
                description: 返回值,0正常, 其他皆为异常
                example: 0
              data:
                $ref: '#/components/schemas/ApiAction'
    FacePinchWorkGetByShareIdRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: integer
                description: 返回值,0正常, 其他皆为异常
                example: 0
              data:
                allOf:
                  - $ref: '#/components/schemas/FacePinchWorkAddRetTiny'
                  - $ref: '#/components/schemas/FacePinchWorkAdd'
                  - $ref: '#/components/schemas/FacePinchWorkExtraForAccuse'
    ActivityLoginValidateAccessTokenRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                type: object
                required:
                  - code
                  - data
                properties:
                  roleId:
                    type: string
                    example: *********
                  issueTime:
                    type: number
                    example: 1673332556
    CardNotionAddRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/CardNotionAdd'
    CardRedDotRes:
      description: OK
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/ApiArrOkRes'
              - type: object
                properties:
                  data:
                    description: 有新消息的卡片id列表
                    type: array
                    items:
                      type: number
                      example: 10001
    CardListInfoRes:
      description: OK
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/ApiArrOkRes'
              - type: object
                properties:
                  code:
                    type: integer
                    description: 返回值,0正常, 其他皆为异常
                    example: 0
                  data:
                    description: 卡片列表详情信息
                    type: array
                    items:
                      $ref: '#/components/schemas/CardInfo'
    CardInfoRes:
      description: OK
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/ApiOkRes'
              - type: object
                properties:
                  data:
                    $ref: '#/components/schemas/CardInfo'
    CardLikeIdListRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
                example: 0
              data:
                type: object
                required:
                  - ids
                properties:
                  ids:
                    type: array
                    items:
                      type: number
                      example: 2145
    CardLikeListRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                type: object
                required:
                  - list
                properties:
                  list:
                    type: array
                    description: 点赞卡片列表
                    items:
                      $ref: '#/components/schemas/CardLikeItem'
    CardNotionShowRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/CardNotionItemDetailShow'
    CardNotionListRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                type: object
                required:
                  - list
                  - count
                properties:
                  list:
                    type: array
                    description: 想法列表
                    items:
                      $ref: '#/components/schemas/CardNotionItemShow'
                  card:
                    $ref: '#/components/schemas/CardInfo'
                  count:
                    type: number
                    description: 想法数量
                    example: 3
    CardNotificationNewNumRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                type: object
                required:
                  - count
                properties:
                  count:
                    type: number
                    description: 新消息数量数量
                    example: 3
    CardNotificationListRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                type: object
                required:
                  - list
                  - count
                properties:
                  list:
                    type: array
                    description: 通知列表
                    items:
                      $ref: '#/components/schemas/CardNotificationShow'
                  card:
                    $ref: '#/components/schemas/CardInfo'
                  count:
                    type: number
                    description: 通知数量
                    example: 3
    CardNotionCommentListRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                type: object
                required:
                  - list
                  - count
                properties:
                  list:
                    type: array
                    description: 想法列表
                    items:
                      $ref: '#/components/schemas/CardNotionCommentShow'
                  card:
                    $ref: '#/components/schemas/CardInfo'
                  count:
                    type: number
                    description: 想法数量
                    example: 3
    CardIsEnableRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
                example: 0
              data:
                type: object
                properties:
                  isEnable:
                    type: boolean
                    description: 是否对玩家开启了剧情卡功能
                    example: true
    CardDiscoverRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
                example: 0
              data:
                type: array
                items:
                  type: number
                  description: 卡片id
                  example: 15002
    EquipWeaponsRankRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/EquipWeaponsRankData'
    ComplainAddRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/ComplainAddData'
    WeaponFiltersRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/WeaponFilterData'
    EquipCommentListRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/EquipCommentListData'
    SlientLoveAddRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/SlientLoveAdd'
    ApiActionRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/ApiAction'
    ApiOkRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/ApiOkData'
    GetLocationRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/GetLocationData'
    CommonAddRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/CommonAdd'
    res-inform-list:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                type: object
                properties:
                  list:
                    type: array
                    items:
                      $ref: '#/components/schemas/inform'
    res-inform-news-num:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                type: object
                properties:
                  inform:
                    type: number
                    description: 新通知数
                    example: 0
                  message:
                    type: number
                    description: 新消息数
                    example: 0
                  newinform:
                    $ref: '#/components/schemas/inform'
    res-moment-lottery-show:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/moment-lottery-show'
